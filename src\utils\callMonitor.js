/**
 * 通话监听服务
 * 负责监听通话状态变化并触发相应的精灵系统更新
 */

import store from '@/store'
import callTimeTracker from './callTimeTracker'

class CallMonitor {
  constructor() {
    this.isInitialized = false
    this.plugins = null
    this.callStateCallback = null
  }

  /**
   * 初始化通话监听
   * @param {Object} plugins - jssdk插件对象
   */
  async init(plugins) {
    if (this.isInitialized) {
      console.warn('通话监听已经初始化')
      return
    }

    this.plugins = plugins
    
    if (!plugins || !plugins.callbackPlugin) {
      throw new Error('无法获取通话插件')
    }

    try {
      // 注册通话状态监听回调
      this.callStateCallback = (data) => {
        this.handleCallStateChange(data)
      }

      plugins.callbackPlugin.callStateNotified(this.callStateCallback)
      
      // 设置通话时长追踪器回调
      this.setupCallTimeTrackerCallbacks()
      
      this.isInitialized = true
      console.log('通话监听初始化成功')
      
    } catch (error) {
      console.error('通话监听初始化失败:', error)
      throw error
    }
  }

  /**
   * 设置通话时长追踪器回调
   */
  setupCallTimeTrackerCallbacks() {
    // 通话开始回调
    callTimeTracker.on('start', (startTime) => {
      console.log('通话开始:', new Date(startTime))
      store.dispatch('sprite/startCallTimer')
    })

    // 通话结束回调
    callTimeTracker.on('end', (callInfo) => {
      console.log('通话结束:', callInfo)
      store.dispatch('sprite/endCallTimer')
      
      // 如果通话时长有效，记录通话记录
      if (callInfo.durationMinutes > 0) {
        this.recordCallHistory(callInfo)
      }
    })

    // 通话时长更新回调
    callTimeTracker.on('update', (updateInfo) => {
      // 实时更新store中的通话时长
      store.commit('sprite/UPDATE_CURRENT_CALL_DURATION', updateInfo.durationSeconds)
    })
  }

  /**
   * 处理通话状态变化
   * @param {Object} data - 通话状态数据
   */
  handleCallStateChange(data) {
    try {
      console.log('收到通话状态变化:', data)
      
      const callState = data.callInfo && data.callInfo.callState
      const callId = data.callInfo && data.callInfo.callId
      
      if (!callState) {
        console.warn('无效的通话状态数据')
        return
      }

      switch (callState) {
        case 'CONNECTED':
          this.handleCallConnected(data)
          break
        case 'DISCONNECTED':
          this.handleCallDisconnected(data)
          break
        case 'FAILED':
          this.handleCallFailed(data)
          break
        case 'RINGING':
          this.handleCallRinging(data)
          break
        default:
          console.log('未处理的通话状态:', callState)
      }
      
    } catch (error) {
      console.error('处理通话状态变化失败:', error)
    }
  }

  /**
   * 处理通话连接
   */
  handleCallConnected(data) {
    console.log('通话已连接')
    
    // 检查用户是否有精灵
    const currentSprite = store.getters['sprite/currentSprite']
    if (!currentSprite) {
      console.log('用户没有精灵，不开始计时')
      return
    }
    
    // 开始通话计时
    callTimeTracker.start()
  }

  /**
   * 处理通话断开
   */
  handleCallDisconnected(data) {
    console.log('通话已断开')
    
    // 结束通话计时
    if (callTimeTracker.isActive()) {
      callTimeTracker.end()
    }
  }

  /**
   * 处理通话失败
   */
  handleCallFailed(data) {
    console.log('通话失败')
    
    // 结束通话计时
    if (callTimeTracker.isActive()) {
      callTimeTracker.end()
    }
  }

  /**
   * 处理通话响铃
   */
  handleCallRinging(data) {
    console.log('通话响铃中')
    // 响铃状态暂时不需要特殊处理
  }

  /**
   * 记录通话历史
   * @param {Object} callInfo - 通话信息
   */
  async recordCallHistory(callInfo) {
    try {
      const dataStorage = (await import('./dataStorage')).default
      
      const callRecord = {
        startTime: callInfo.startTime,
        endTime: callInfo.endTime,
        duration: callInfo.duration,
        durationMinutes: callInfo.durationMinutes,
        experienceGained: callInfo.durationMinutes * 10,
        spriteId: store.getters['sprite/currentSprite'] && store.getters['sprite/currentSprite'].id,
        timestamp: Date.now()
      }
      
      await dataStorage.addCallRecord(callRecord)
      console.log('通话记录已保存:', callRecord)
      
    } catch (error) {
      console.error('保存通话记录失败:', error)
    }
  }

  /**
   * 获取当前通话状态
   */
  getCurrentCallState() {
    return {
      isInCall: callTimeTracker.isActive(),
      duration: callTimeTracker.getCurrentDuration(),
      durationMinutes: callTimeTracker.getCurrentDurationMinutes(),
      durationSeconds: callTimeTracker.getCurrentDurationSeconds(),
      formatted: callTimeTracker.formatDuration()
    }
  }

  /**
   * 手动开始通话计时（用于测试）
   */
  startTestCall() {
    if (!this.isInitialized) {
      console.error('通话监听未初始化')
      return
    }
    
    console.log('开始测试通话')
    this.handleCallConnected({ callInfo: { callState: 'CONNECTED', callId: 'test' } })
  }

  /**
   * 手动结束通话计时（用于测试）
   */
  endTestCall() {
    if (!this.isInitialized) {
      console.error('通话监听未初始化')
      return
    }
    
    console.log('结束测试通话')
    this.handleCallDisconnected({ callInfo: { callState: 'DISCONNECTED', callId: 'test' } })
  }

  /**
   * 销毁监听器
   */
  destroy() {
    if (callTimeTracker.isActive()) {
      callTimeTracker.end()
    }
    
    // 清理回调
    if (this.plugins && this.plugins.callbackPlugin && this.callStateCallback) {
      // 注意：这里假设jssdk提供了移除回调的方法
      // 实际实现可能需要根据jssdk的API调整
      try {
        if (this.plugins.callbackPlugin.removeCallStateCallback) {
          this.plugins.callbackPlugin.removeCallStateCallback(this.callStateCallback)
        }
      } catch (error) {
        console.warn('移除通话状态回调失败:', error)
      }
    }
    
    this.isInitialized = false
    this.plugins = null
    this.callStateCallback = null
    
    console.log('通话监听已销毁')
  }
}

// 创建全局实例
const callMonitor = new CallMonitor()

export default callMonitor

// 导出便捷函数
export const initCallMonitor = (plugins) => callMonitor.init(plugins)
export const getCurrentCallState = () => callMonitor.getCurrentCallState()
export const startTestCall = () => callMonitor.startTestCall()
export const endTestCall = () => callMonitor.endTestCall()

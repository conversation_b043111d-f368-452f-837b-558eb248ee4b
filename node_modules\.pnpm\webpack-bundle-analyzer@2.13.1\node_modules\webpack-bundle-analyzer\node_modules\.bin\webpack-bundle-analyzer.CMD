@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules\webpack-bundle-analyzer\lib\bin\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules\webpack-bundle-analyzer\lib\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules\webpack-bundle-analyzer\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules\webpack-bundle-analyzer\lib\bin\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules\webpack-bundle-analyzer\lib\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules\webpack-bundle-analyzer\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\webpack-bundle-analyzer@2.13.1\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\lib\bin\analyzer.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\lib\bin\analyzer.js" %*
)

// 精灵动画样式

// 投喂动画
@keyframes feedAnimation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  50% {
    transform: scale(1.2) rotate(5deg);
  }
  75% {
    transform: scale(1.1) rotate(-2deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

// 升级特效
@keyframes levelUpEffect {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 闪光特效
@keyframes sparkleEffect {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

// 经验值增长动画
@keyframes expGrowth {
  0% {
    width: var(--start-width);
  }
  100% {
    width: var(--end-width);
  }
}

// 精灵呼吸动画
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 按钮点击反馈
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

// 甜甜圈掉落动画
@keyframes donutFall {
  0% {
    transform: translateY(-50px) rotate(0deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(0) rotate(360deg);
    opacity: 0;
  }
}

// 爱心飘浮动画
@keyframes heartFloat {
  0% {
    transform: translateY(0) scale(0);
    opacity: 0;
  }
  50% {
    transform: translateY(-30px) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(-60px) scale(0);
    opacity: 0;
  }
}

// 星星闪烁动画
@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// 动画类
.feed-animation {
  animation: feedAnimation 0.6s ease-in-out;
}

.level-up-effect {
  animation: levelUpEffect 1s ease-in-out;
}

.sparkle-effect {
  animation: sparkleEffect 1.5s ease-in-out infinite;
}

.exp-growth {
  animation: expGrowth 1s ease-out;
}

.breathe-animation {
  animation: breathe 3s ease-in-out infinite;
}

.button-press {
  animation: buttonPress 0.2s ease-in-out;
}

.donut-fall {
  animation: donutFall 1s ease-in-out;
}

.heart-float {
  animation: heartFloat 2s ease-out;
}

.star-twinkle {
  animation: starTwinkle 2s ease-in-out infinite;
}

// 特效容器
.effect-container {
  position: relative;
  overflow: hidden;
  
  .particle {
    position: absolute;
    pointer-events: none;
    
    &.donut {
      width: 20px;
      height: 20px;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF6B6B"><circle cx="12" cy="12" r="10" fill="%23FFD93D"/><circle cx="12" cy="12" r="4" fill="white"/></svg>');
      background-size: contain;
    }
    
    &.heart {
      width: 16px;
      height: 16px;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF69B4"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>');
      background-size: contain;
    }
    
    &.star {
      width: 12px;
      height: 12px;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>');
      background-size: contain;
    }
  }
}

// 页面过渡动画
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s ease;
}

.page-transition-enter {
  opacity: 0;
  transform: translateX(30px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 弹窗动画
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter {
  opacity: 0;
  transform: scale(0.8);
}

.modal-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

// 加载动画
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: loading 1s linear infinite;
  margin: 20px auto;
}

// 成功提示动画
@keyframes successPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-animation {
  animation: successPulse 0.6s ease-in-out;
}

// 错误提示动画
@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.error-animation {
  animation: errorShake 0.5s ease-in-out;
}

// 升级光效动画
@keyframes levelUpGlow {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

// 星星爆炸动画
@keyframes starBurst {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5) rotate(360deg);
    margin-left: 100px;
    margin-top: 0px;
  }
}

// 经验值飘浮动画
@keyframes expGainFloat {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-40px);
  }
}

// 升级消息动画
@keyframes levelUpMessage {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px) scale(0.8);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1.1);
  }
  80% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.9);
  }
}

// 甜甜圈粒子动画
.donut-particle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  position: absolute;
  animation: donutFall 1.5s ease-out forwards;

  &::before {
    content: '🍩';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
  }
}

// 爱心粒子样式
.heart-particle {
  position: absolute;
  font-size: 16px;
  animation: heartFloat 2s ease-out forwards;
}

// 水滴粒子样式
.water-droplet {
  width: 8px;
  height: 12px;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: absolute;
  animation: waterDrop 1.2s ease-out forwards;
}

// 闪光粒子样式
.glow-particle {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  position: absolute;
  animation: starTwinkle 2s ease-in-out infinite;
}

// 升级光效样式
.level-up-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  pointer-events: none;
}

// 星星爆炸样式
.star-burst {
  position: absolute;
  font-size: 12px;
  pointer-events: none;
}

// 经验值增长指示器
.exp-gain-indicator {
  position: absolute;
  font-weight: bold;
  pointer-events: none;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
}

// 升级消息样式
.level-up-message {
  position: absolute;
  white-space: nowrap;
  pointer-events: none;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);

  i {
    margin-right: 4px;
  }
}

// 特效容器样式
.effect-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;

  .particle {
    position: absolute;
    pointer-events: none;
  }
}

// 连击效果动画
@keyframes comboEffect {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.combo-effect {
  animation: comboEffect 0.4s ease-in-out;
}

// 响应式动画调整
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

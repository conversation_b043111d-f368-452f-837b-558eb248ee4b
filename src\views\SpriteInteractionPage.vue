<template>
  <div class="sprite-container">
    <!-- 精灵信息卡片 -->
    <div class="sprite-card">
      <div class="sprite-info-section">
        <!-- 精灵级别显示 -->
        <div class="level-display">
          <span class="level-label">Lv.</span>
          <span class="level-number">{{ currentLevel }}</span>
          <span class="form-indicator">({{ formDescription }})</span>
        </div>

        <!-- 经验条 -->
        <div class="experience-section">
          <ExperienceBar
            :current="experienceProgress.current"
            :total="experienceProgress.total"
            :percentage="experienceProgress.percentage"
            :needed="experienceProgress.needed"
            @level-up="handleLevelUp"
          />
        </div>

        <!-- 精灵形象展示 -->
        <div class="sprite-display">
          <div class="sprite-avatar-container">
            <img 
              :src="currentSpriteImage" 
              :alt="currentSpriteName"
              class="sprite-avatar"
              :class="{
                'glow-effect': hasGlowEffect,
                'breathe-animation': !isInteracting
              }"
              @error="handleImageError"
            />
            
            <!-- 闪光特效 -->
            <div v-if="hasGlowEffect" class="glow-particles">
              <div class="particle" v-for="i in 8" :key="i"></div>
            </div>
            
            <!-- 互动特效容器 -->
            <div class="effect-container" ref="effectContainer">
              <!-- 动态生成的特效元素会在这里显示 -->
            </div>
          </div>
        </div>

        <!-- 精灵名字 -->
        <div class="sprite-name-section">
          <SpriteNameEditor 
            :name="currentSpriteName"
            :editable="true"
            @name-change="handleNameChange"
          />
        </div>
      </div>
    </div>

    <!-- 互动按钮区域 -->
    <div class="sprite-card">
      <div class="card-title">互动</div>
      <InteractionButtons 
        :sprite="currentSprite"
        :can-feed="canFeedToday"
        :is-interacting="isInteracting"
        @feed="handleFeed"
        @touch="handleTouch"
        @bath="handleBath"
      />
    </div>

    <!-- 功能按钮区域 -->
    <div class="sprite-card">
      <div class="function-buttons">
        <el-button 
          class="function-btn rules-btn"
          @click="showUpgradeRules = true"
        >
          <i class="el-icon-question"></i>
          等级提升规则
        </el-button>
        
        <el-button 
          class="function-btn change-sprite-btn"
          @click="goToSpriteSelection"
        >
          <i class="el-icon-refresh"></i>
          更换精灵
        </el-button>
      </div>
    </div>

    <!-- 通话状态显示 -->
    <div v-if="callState.isInCall" class="sprite-card call-status">
      <div class="card-title">
        <i class="el-icon-phone"></i>
        通话中
      </div>
      <div class="call-info">
        <p class="call-duration">通话时长: {{ formatCallDuration }}</p>
        <p class="exp-gaining">正在获得经验值...</p>
      </div>
    </div>

    <!-- 升级规则弹窗 -->
    <SpriteUpgradeRules 
      :visible="showUpgradeRules"
      @close="showUpgradeRules = false"
    />

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载精灵信息...</p>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import ExperienceBar from '@/components/ExperienceBar.vue'
import SpriteNameEditor from '@/components/SpriteNameEditor.vue'
import InteractionButtons from '@/components/InteractionButtons.vue'
import SpriteUpgradeRules from '@/components/SpriteUpgradeRules.vue'
import { getFormDescription } from '@/utils/spriteCalculator'
import { checkCanFeed } from '@/utils/spriteCalculator'
import animationManager from '@/utils/animationManager'

export default {
  name: 'SpriteInteractionPage',
  components: {
    ExperienceBar,
    SpriteNameEditor,
    InteractionButtons,
    SpriteUpgradeRules
  },
  data() {
    return {
      isLoading: false,
      isInteracting: false,
      showUpgradeRules: false,
      callDurationTimer: null
    }
  },
  computed: {
    ...mapState('sprite', ['user', 'sprites', 'spriteTypes', 'callState']),
    ...mapGetters('sprite', [
      'currentSprite', 
      'currentSpriteLevel', 
      'currentSpriteForm', 
      'experienceProgress',
      'hasGlowEffect'
    ]),

    /**
     * 当前精灵名字
     */
    currentSpriteName() {
      return this.currentSprite ? this.currentSprite.name : '通话精灵'
    },

    /**
     * 当前等级
     */
    currentLevel() {
      return this.currentSpriteLevel
    },

    /**
     * 形态描述
     */
    formDescription() {
      return getFormDescription(this.currentSpriteForm)
    },

    /**
     * 当前精灵图片
     */
    currentSpriteImage() {
      if (!this.currentSprite) {
        return require('@/assets/images/sprites/placeholder.svg')
      }
      
      try {
        return require(`@/assets/images/sprites/sprite${this.currentSprite.typeId}_form${this.currentSpriteForm}.png`)
      } catch (error) {
        return require('@/assets/images/sprites/placeholder.svg')
      }
    },

    /**
     * 是否可以投喂
     */
    canFeedToday() {
      const feedCheck = checkCanFeed(this.user.lastFeedDate)
      return feedCheck.willGetExp
    },

    /**
     * 格式化通话时长
     */
    formatCallDuration() {
      if (!this.callState.isInCall) return '00:00'
      
      const duration = this.callState.currentCallDuration
      const minutes = Math.floor(duration / 60)
      const seconds = duration % 60
      
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  },
  async mounted() {
    await this.initializePage()
    this.startCallDurationTimer()
    this.checkGlowEffect()
  },
  beforeDestroy() {
    this.stopCallDurationTimer()
  },
  methods: {
    ...mapActions('sprite', [
      'initializeSpriteData',
      'feedSprite',
      'switchSprite'
    ]),

    /**
     * 初始化页面
     */
    async initializePage() {
      this.isLoading = true
      
      try {
        await this.initializeSpriteData()
        
        // 检查是否有当前精灵
        if (!this.currentSprite) {
          this.$message.warning('请先认领一只精灵')
          this.$router.push('/sprite-claim')
          return
        }
        
        console.log('精灵互动页面初始化完成')
        
      } catch (error) {
        console.error('页面初始化失败:', error)
        this.$message.error('初始化失败，请重试')
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 处理精灵名字修改
     */
    async handleNameChange(newName) {
      try {
        if (!this.currentSprite) {
          this.$message.error('没有当前精灵')
          return
        }

        await this.$store.dispatch('sprite/updateSpriteName', {
          spriteId: this.currentSprite.id,
          name: newName
        })

        this.$message.success('名字修改成功！')
      } catch (error) {
        console.error('名字修改失败:', error)
        this.$message.error(error.message || '名字修改失败')
      }
    },

    /**
     * 处理投喂
     */
    async handleFeed() {
      if (this.isInteracting) return
      
      this.isInteracting = true
      
      try {
        const result = await this.feedSprite()
        
        if (result.success) {
          this.$message.success(result.message)
          this.playFeedAnimation()
        } else {
          this.$message.info(result.message)
          this.playFeedAnimation(false) // 只播放动画，不增加经验
        }
        
      } catch (error) {
        console.error('投喂失败:', error)
        this.$message.error('投喂失败，请重试')
      } finally {
        setTimeout(() => {
          this.isInteracting = false
        }, 1000)
      }
    },

    /**
     * 处理抚摸
     */
    handleTouch() {
      if (this.isInteracting) return
      
      this.isInteracting = true
      this.playTouchAnimation()
      
      setTimeout(() => {
        this.isInteracting = false
      }, 1000)
    },

    /**
     * 处理洗澡
     */
    handleBath() {
      if (this.isInteracting) return
      
      this.isInteracting = true
      this.playBathAnimation()
      
      setTimeout(() => {
        this.isInteracting = false
      }, 1000)
    },

    /**
     * 播放投喂动画
     */
    playFeedAnimation(hasExp = true) {
      const avatarContainer = this.$el.querySelector('.sprite-avatar-container')
      if (avatarContainer) {
        animationManager.playFeedAnimation(avatarContainer, hasExp)
      }
    },

    /**
     * 播放抚摸动画
     */
    playTouchAnimation() {
      const avatarContainer = this.$el.querySelector('.sprite-avatar-container')
      if (avatarContainer) {
        animationManager.playTouchAnimation(avatarContainer)
      }
    },

    /**
     * 播放洗澡动画
     */
    playBathAnimation() {
      const avatarContainer = this.$el.querySelector('.sprite-avatar-container')
      if (avatarContainer) {
        animationManager.playBathAnimation(avatarContainer)
      }
    },

    /**
     * 播放升级动画
     */
    playLevelUpAnimation(newLevel) {
      const avatarContainer = this.$el.querySelector('.sprite-avatar-container')
      if (avatarContainer) {
        animationManager.playLevelUpAnimation(avatarContainer, newLevel)
      }
    },

    /**
     * 播放闪光特效
     */
    playGlowEffect() {
      const avatarContainer = this.$el.querySelector('.sprite-avatar-container')
      if (avatarContainer) {
        animationManager.playGlowEffect(avatarContainer)
      }
    },

    /**
     * 处理升级事件
     */
    handleLevelUp(newLevel) {
      console.log('精灵升级到:', newLevel)

      // 播放升级动画
      this.playLevelUpAnimation(newLevel)

      // 显示升级提示
      this.$message.success(`恭喜！精灵升级到 Lv.${newLevel}！`)

      // 检查是否需要形态变化
      const newForm = this.calculateFormByLevel(newLevel)
      const currentForm = this.currentSpriteForm

      if (newForm !== currentForm) {
        setTimeout(() => {
          this.$message.info(`精灵进化为${this.getFormDescription(newForm)}！`)
        }, 1500)
      }
    },

    /**
     * 根据等级计算形态
     */
    calculateFormByLevel(level) {
      if (level <= 5) return 1
      if (level <= 15) return 2
      return 3
    },

    /**
     * 获取形态描述
     */
    getFormDescription(form) {
      const descriptions = {
        1: '幼体形态',
        2: '成长形态',
        3: '完全体形态'
      }
      return descriptions[form] || '未知形态'
    },

    /**
     * 检查闪光特效
     */
    checkGlowEffect() {
      if (this.hasGlowEffect) {
        // 延迟一秒后开始闪光特效
        setTimeout(() => {
          this.playGlowEffect()

          // 每10秒重复一次闪光特效
          setInterval(() => {
            if (this.hasGlowEffect) {
              this.playGlowEffect()
            }
          }, 10000)
        }, 1000)
      }
    },

    /**
     * 跳转到精灵选择页面
     */
    goToSpriteSelection() {
      this.$router.push('/sprite-selection')
    },

    /**
     * 开始通话时长计时器
     */
    startCallDurationTimer() {
      this.callDurationTimer = setInterval(() => {
        if (this.callState.isInCall) {
          // 更新通话时长显示
          this.$forceUpdate()
        }
      }, 1000)
    },

    /**
     * 停止通话时长计时器
     */
    stopCallDurationTimer() {
      if (this.callDurationTimer) {
        clearInterval(this.callDurationTimer)
        this.callDurationTimer = null
      }
    },

    /**
     * 处理图片加载错误
     */
    handleImageError(event) {
      event.target.src = require('@/assets/images/sprites/placeholder.svg')
    }
  }
}
</script>

<style lang="less" scoped>
@import '../styles/sprite.less';
@import '../styles/animations.less';

.sprite-info-section {
  text-align: center;
  
  .level-display {
    margin-bottom: 16px;
    
    .level-label {
      font-size: 16px;
      color: #666;
      margin-right: 4px;
    }
    
    .level-number {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
    }
    
    .form-indicator {
      font-size: 12px;
      color: #999;
      margin-left: 8px;
    }
  }
  
  .experience-section {
    margin-bottom: 24px;
  }
  
  .sprite-display {
    margin-bottom: 20px;
    
    .sprite-avatar-container {
      position: relative;
      display: inline-block;
      
      .sprite-avatar {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        border: 4px solid #e0e0e0;
        transition: all 0.3s ease;
        
        &.glow-effect {
          border-color: #ffd700;
          box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
        }
      }
      
      .glow-particles {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        
        .particle {
          position: absolute;
          width: 6px;
          height: 6px;
          background: #ffd700;
          border-radius: 50%;
          animation: starTwinkle 2s ease-in-out infinite;
          
          &:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
          &:nth-child(2) { top: 20%; right: 15%; animation-delay: 0.3s; }
          &:nth-child(3) { bottom: 30%; left: 10%; animation-delay: 0.6s; }
          &:nth-child(4) { bottom: 20%; right: 20%; animation-delay: 0.9s; }
          &:nth-child(5) { top: 50%; left: 5%; animation-delay: 1.2s; }
          &:nth-child(6) { top: 40%; right: 5%; animation-delay: 1.5s; }
          &:nth-child(7) { bottom: 50%; left: 50%; animation-delay: 1.8s; }
          &:nth-child(8) { top: 30%; left: 50%; animation-delay: 2.1s; }
        }
      }
      
      .effect-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        overflow: hidden;
        border-radius: 50%;
      }
    }
  }
  
  .sprite-name-section {
    margin-bottom: 16px;
  }
}

.call-status {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .card-title {
    color: white;
    
    i {
      margin-right: 8px;
    }
  }
  
  .call-info {
    text-align: center;
    
    .call-duration {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .exp-gaining {
      font-size: 14px;
      opacity: 0.9;
      margin: 0;
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  p {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }
}
</style>

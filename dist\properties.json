{"appId": "com.cmcc.miniappdemo", "version": "1.0.0", "category": "", "priority": 0, "permissions": ["MINIAPP_EXTERNAL_STORAGE", "MINIAPP_ACCESS_LOCATION", "MINIAPP_ACCESS_MINIAPPINFO", "MINIAPP_CAMERA", "MINIAPP_RECORD_AUDIO", "MINIAPP_READ_CONTACTS", "MINIAPP_GBA"], "windowStyle": {"transparency": 100, "isWindowFront": true, "height": -1, "width": -1, "windowTop": 0, "windowStart": 0, "statusBarColor": "#FFFFFF", "navigationBarColor": "#FFFFFF", "barEnable": false, "isShowTitleBar": true, "isFullScreen": false}, "controlBarStyle": {"gravity": 1, "isShowControlBar": true, "top": 24, "bottom": 24, "start": 18, "end": 18}}
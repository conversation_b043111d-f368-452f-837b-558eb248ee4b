webpackJsonp([2],{DdYH:function(e,t,i){var s={"./sprite1_form1.png":"oLbb"};function r(e){return i(n(e))}function n(e){var t=s[e];if(!(t+1))throw new Error("Cannot find module '"+e+"'.");return t}r.keys=function(){return Object.keys(s)},r.resolve=n,e.exports=r,r.id="DdYH"},fLNo:function(e,t){},gFrU:function(e,t,i){"use strict";t.a=function(e){var t=(new Date).toDateString();if(!e||e!==t)return{canFeed:!0,willGetExp:!0,message:"可以投喂，将获得经验值奖励！"};return{canFeed:!0,willGetExp:!1,message:"今天已经投喂过了，只能看动画哦~"}},t.b=function(e){if(e<60)return e+"分钟";var t=Math.floor(e/60),i=e%60;if(0===i)return t+"小时";return t+"小时"+i+"分钟"},t.c=s;function s(e){return{1:"幼体形态",2:"成长形态",3:"完全体形态"}[e]||"未知形态"}},"uVB+":function(e,t){e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMjAiIGZpbGw9IiNGNUY1RjUiLz4KICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIzNSIgZmlsbD0iI0UwRTBFMCIvPgogIDxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjQiIGZpbGw9IiMzMzMiLz4KICA8Y2lyY2xlIGN4PSI3MCIgY3k9IjUwIiByPSI0IiBmaWxsPSIjMzMzIi8+CiAgPHBhdGggZD0iTTQ1IDc1UTYwIDg1IDc1IDc1IiBzdHJva2U9IiMzMzMiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgogIDx0ZXh0IHg9IjYwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgZmlsbD0iIzY2NiI+57K+54G1PC90ZXh0Pgo8L3N2Zz4K"},"zax/":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=i("s/F+"),r=i.n(s),n=i("yrbl"),a=i.n(n),c=i("SaMe"),l=i.n(c),o=i("vvmr"),p=i.n(o),d=i("nVSy"),u=i("gFrU"),v={name:"SpriteSelectionPage",data:function(){return{selectedSpriteType:null,isProcessing:!1,showSpriteDetail:!1,selectedSpriteDetail:null}},computed:p()({},Object(d.d)("sprite",["user","sprites","spriteTypes"]),Object(d.c)("sprite",["unlockedSpriteTypes"])),mounted:function(){if(this.user.currentSpriteId){var e=this.sprites[this.user.currentSpriteId];e&&(this.selectedSpriteType=e.typeId)}},methods:p()({},Object(d.b)("sprite",["claimSprite","switchSprite"]),{isSpriteUnlocked:function(e){return this.user.totalCallTime>=this.getSpriteType(e).unlockRequirement},isSpritOwned:function(e){return l()(this.sprites).some(function(t){return t.typeId===e})},isCurrentSprite:function(e){if(!this.user.currentSpriteId)return!1;var t=this.sprites[this.user.currentSpriteId];return t&&t.typeId===e},getSpriteType:function(e){return this.spriteTypes.find(function(t){return t.id===e})||{}},getSpriteImage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;try{return i("DdYH")("./sprite"+e+"_form"+t+".png")}catch(e){return i("uVB+")}},handleSpriteSelect:function(e){this.isSpriteUnlocked(e.id)?(this.selectedSpriteType=e.id,this.selectedSpriteDetail&&this.selectedSpriteDetail.id===e.id?this.showSpriteDetail=!0:this.selectedSpriteDetail=e):this.$message.warning("此精灵尚未解锁")},handleConfirmSelection:function(){var e=this;return a()(r.a.mark(function t(){var i;return r.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.selectedSpriteType){t.next=2;break}return t.abrupt("return");case 2:if(e.isProcessing=!0,t.prev=3,!e.isSpritOwned(e.selectedSpriteType)){t.next=11;break}return i=l()(e.sprites).find(function(t){return t.typeId===e.selectedSpriteType}),t.next=8,e.switchSprite(i.id);case 8:e.$message.success("精灵切换成功！"),t.next=14;break;case 11:return t.next=13,e.claimSprite(e.selectedSpriteType);case 13:e.$message.success("恭喜获得新精灵！");case 14:e.$router.push("/sprite-interaction"),t.next=21;break;case 17:t.prev=17,t.t0=t.catch(3),console.error("精灵选择失败:",t.t0),e.$message.error("操作失败，请重试");case 21:return t.prev=21,e.isProcessing=!1,t.finish(21);case 24:case"end":return t.stop()}},t,e,[[3,17,21,24]])}))()},goBack:function(){this.$router.go(-1)},formatDuration:function(e){return Object(u.b)(e)},handleImageError:function(e){e.target.src=i("uVB+")}})},S={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"sprite-container"},[e._m(0),e._v(" "),i("div",{staticClass:"sprite-card"},[i("div",{staticClass:"card-title"},[e._v("可用精灵")]),e._v(" "),i("div",{staticClass:"sprite-selection-grid"},e._l(e.spriteTypes,function(t){return i("div",{key:t.id,staticClass:"sprite-option",class:{selected:e.selectedSpriteType===t.id,locked:!e.isSpriteUnlocked(t.id),owned:e.isSpritOwned(t.id),current:e.isCurrentSprite(t.id)},on:{click:function(i){return e.handleSpriteSelect(t)}}},[e.isSpriteUnlocked(t.id)?i("div",{staticClass:"sprite-content"},[i("img",{staticClass:"sprite-preview",attrs:{src:e.getSpriteImage(t.id,1),alt:t.name},on:{error:e.handleImageError}}),e._v(" "),i("h3",{staticClass:"sprite-name"},[e._v(e._s(t.name))]),e._v(" "),i("div",{staticClass:"status-badges"},[e.isCurrentSprite(t.id)?i("span",{staticClass:"badge current-badge"},[e._v("\n              当前携带\n            ")]):e.isSpritOwned(t.id)?i("span",{staticClass:"badge owned-badge"},[e._v("\n              已拥有\n            ")]):i("span",{staticClass:"badge available-badge"},[e._v("\n              可获得\n            ")])]),e._v(" "),i("div",{staticClass:"sprite-info"},[i("p",{staticClass:"unlock-info"},[e._v("\n              解锁条件: "+e._s(0===t.unlockRequirement?"初始精灵":"通话"+e.formatDuration(t.unlockRequirement))+"\n            ")])])]):i("div",{staticClass:"lock-overlay"},[i("i",{staticClass:"el-icon-lock lock-icon"}),e._v(" "),i("p",{staticClass:"unlock-requirement"},[e._v("\n            需要累计通话 "+e._s(e.formatDuration(t.unlockRequirement))+"\n          ")]),e._v(" "),i("p",{staticClass:"current-progress"},[e._v("\n            当前进度: "+e._s(e.formatDuration(e.user.totalCallTime))+"\n          ")])])])}),0)]),e._v(" "),i("div",{staticClass:"action-buttons"},[e.selectedSpriteType&&!e.isCurrentSprite(e.selectedSpriteType)?i("el-button",{attrs:{type:"primary",size:"large",loading:e.isProcessing},on:{click:e.handleConfirmSelection}},[e._v("\n      "+e._s(e.isSpritOwned(e.selectedSpriteType)?"切换到此精灵":"获得此精灵")+"\n    ")]):e._e(),e._v(" "),i("el-button",{attrs:{size:"large"},on:{click:e.goBack}},[e._v("\n      返回\n    ")])],1),e._v(" "),i("el-dialog",{attrs:{title:"精灵详情",visible:e.showSpriteDetail,width:"80%",center:""},on:{"update:visible":function(t){e.showSpriteDetail=t}}},[e.selectedSpriteDetail?i("div",{staticClass:"sprite-detail-content"},[i("div",{staticClass:"detail-header"},[i("img",{staticClass:"detail-avatar",attrs:{src:e.getSpriteImage(e.selectedSpriteDetail.id,1),alt:e.selectedSpriteDetail.name}}),e._v(" "),i("h2",[e._v(e._s(e.selectedSpriteDetail.name))])]),e._v(" "),i("div",{staticClass:"detail-forms"},[i("h3",[e._v("成长形态")]),e._v(" "),i("div",{staticClass:"forms-grid"},e._l(e.selectedSpriteDetail.forms,function(t,s){return i("div",{key:s,staticClass:"form-item"},[i("img",{staticClass:"form-image",attrs:{src:e.getSpriteImage(e.selectedSpriteDetail.id,s),alt:e.selectedSpriteDetail.name+" 形态"+s}}),e._v(" "),i("p",{staticClass:"form-level"},[e._v(e._s(t.level[0])+"-"+e._s(t.level[1])+"级")])])}),0)])]):e._e(),e._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.showSpriteDetail=!1}}},[e._v("关闭")])],1)])],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"sprite-header"},[t("h1",{staticClass:"title"},[this._v("选择你的精灵")]),this._v(" "),t("p",{staticClass:"subtitle"},[this._v("选择一只精灵陪伴你的通话时光")])])}]};var g=i("l2X3")(v,S,!1,function(e){i("fLNo")},"data-v-4971bcee",null);t.default=g.exports}});
<template>
  <div class="sprite-container">
    <div class="sprite-header">
      <h1 class="title">欢迎来到通话精灵世界</h1>
      <p class="subtitle">开始你的精灵冒险之旅</p>
    </div>

    <div class="sprite-card">
      <div class="claim-content">
        <div class="sprite-preview">
          <img 
            :src="previewSprite.image" 
            alt="精灵预览" 
            class="sprite-avatar"
            @error="handleImageError"
          />
          <div class="sparkle-effects" v-if="showSparkles">
            <div class="sparkle" v-for="i in 6" :key="i"></div>
          </div>
        </div>

        <h2 class="claim-title">认领你的第一只精灵</h2>
        
        <div class="claim-description">
          <p>通话精灵将陪伴你的每一次通话，</p>
          <p>通过通话时长获得经验值，让精灵不断成长！</p>
          <br>
          <div class="feature-list">
            <div class="feature-item">
              <i class="el-icon-phone"></i>
              <span>通话时长转化为经验值</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-food"></i>
              <span>每日投喂获得额外奖励</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-star-on"></i>
              <span>连续通话获得闪光特效</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-trophy"></i>
              <span>解锁更多精灵形象</span>
            </div>
          </div>
        </div>

        <el-button 
          class="claim-btn"
          type="primary"
          size="large"
          :loading="isProcessing"
          @click="handleClaimSprite"
        >
          {{ isProcessing ? '正在认领...' : '领养一个精灵' }}
        </el-button>

        <div class="tips">
          <p class="tip-text">
            <i class="el-icon-info"></i>
            认领后即可开始你的精灵养成之旅
          </p>
        </div>
      </div>
    </div>

    <!-- 加载动画 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在初始化精灵世界...</p>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'

export default {
  name: 'SpriteClaimPage',
  data() {
    return {
      isLoading: false,
      isProcessing: false,
      showSparkles: false,
      previewSprite: {
        image: require('@/assets/images/sprites/sprite1_form1.png'),
        name: '通话精灵'
      }
    }
  },
  computed: {
    ...mapState('sprite', ['spriteTypes'])
  },
  mounted() {
    this.initPage()
    this.startSparkleAnimation()
  },
  methods: {
    ...mapActions('sprite', ['claimSprite', 'initializeSpriteData']),

    /**
     * 初始化页面
     */
    async initPage() {
      this.isLoading = true
      try {
        await this.initializeSpriteData()
        console.log('精灵数据初始化完成')
      } catch (error) {
        console.error('初始化失败:', error)
        this.$message.error('初始化失败，请重试')
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 处理精灵认领
     */
    async handleClaimSprite() {
      if (this.isProcessing) return

      this.isProcessing = true
      
      try {
        // 认领第一个精灵类型（ID为1）
        const newSprite = await this.claimSprite(1)
        
        this.$message.success('恭喜！精灵认领成功！')
        
        // 播放成功动画
        this.playSuccessAnimation()
        
        // 延迟跳转到精灵互动页面
        setTimeout(() => {
          this.$router.push('/sprite-interaction')
        }, 2000)
        
        console.log('精灵认领成功:', newSprite)
        
      } catch (error) {
        console.error('精灵认领失败:', error)
        this.$message.error('认领失败，请重试')
      } finally {
        this.isProcessing = false
      }
    },

    /**
     * 播放成功动画
     */
    playSuccessAnimation() {
      // 添加成功动画类
      const spriteAvatar = this.$el.querySelector('.sprite-avatar')
      if (spriteAvatar) {
        spriteAvatar.classList.add('success-animation')
        
        // 移除动画类
        setTimeout(() => {
          spriteAvatar.classList.remove('success-animation')
        }, 600)
      }
      
      // 显示更多闪光效果
      this.showSparkles = true
      setTimeout(() => {
        this.showSparkles = false
      }, 3000)
    },

    /**
     * 开始闪光动画
     */
    startSparkleAnimation() {
      setInterval(() => {
        this.showSparkles = true
        setTimeout(() => {
          this.showSparkles = false
        }, 2000)
      }, 5000)
    },

    /**
     * 处理图片加载错误
     */
    handleImageError() {
      // 使用默认图片或占位符
      this.previewSprite.image = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNjAiIHI9IjMwIiBmaWxsPSIjRTBFMEUwIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjMiIGZpbGw9IiMzMzMiLz4KPGNpcmNsZSBjeD0iNzAiIGN5PSI1MCIgcj0iMyIgZmlsbD0iIzMzMyIvPgo8cGF0aCBkPSJNNDUgNzBRNjAgODAgNzUgNzAiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo='
    }
  }
}
</script>

<style lang="less" scoped>
@import '../styles/sprite.less';
@import '../styles/animations.less';

.sprite-container {
  .claim-content {
    .sprite-preview {
      position: relative;
      margin-bottom: 32px;
      
      .sprite-avatar {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        display: block;
        margin: 0 auto;
        border: 4px solid #e0e0e0;
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.05);
          border-color: #409eff;
        }
      }
      
      .sparkle-effects {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 150px;
        pointer-events: none;
        
        .sparkle {
          position: absolute;
          width: 8px;
          height: 8px;
          background: #ffd700;
          border-radius: 50%;
          animation: sparkleEffect 1.5s ease-in-out infinite;
          
          &:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
          &:nth-child(2) { top: 20%; right: 15%; animation-delay: 0.3s; }
          &:nth-child(3) { bottom: 30%; left: 10%; animation-delay: 0.6s; }
          &:nth-child(4) { bottom: 20%; right: 20%; animation-delay: 0.9s; }
          &:nth-child(5) { top: 50%; left: 5%; animation-delay: 1.2s; }
          &:nth-child(6) { top: 40%; right: 5%; animation-delay: 1.5s; }
        }
      }
    }
    
    .feature-list {
      text-align: left;
      max-width: 300px;
      margin: 0 auto;
      
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        color: #666;
        
        i {
          margin-right: 12px;
          color: #409eff;
          font-size: 16px;
          width: 20px;
        }
      }
    }
    
    .claim-btn {
      width: 240px;
      height: 50px;
      font-size: 18px;
      border-radius: 25px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      border: none;
      margin: 32px 0 24px;
      
      &:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    .tips {
      .tip-text {
        font-size: 12px;
        color: #999;
        
        i {
          margin-right: 4px;
          color: #409eff;
        }
      }
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  
  p {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }
}

// 成功动画
.success-animation {
  animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(103, 194, 58, 0.6);
  }
  100% {
    transform: scale(1);
  }
}
</style>

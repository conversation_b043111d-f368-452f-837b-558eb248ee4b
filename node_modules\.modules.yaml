hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@javascript-obfuscator/escodegen@2.3.0':
    '@javascript-obfuscator/escodegen': private
  '@javascript-obfuscator/estraverse@5.4.0':
    '@javascript-obfuscator/estraverse': private
  '@types/minimatch@3.0.5':
    '@types/minimatch': private
  '@types/q@1.5.8':
    '@types/q': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@vue/compiler-sfc@2.7.16':
    '@vue/compiler-sfc': private
  accepts@1.3.8:
    accepts: private
  acorn-dynamic-import@2.0.2:
    acorn-dynamic-import: private
  acorn@8.8.2:
    acorn: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  align-text@0.1.4:
    align-text: private
  alphanum-sort@1.0.2:
    alphanum-sort: private
  ansi-html@0.0.7:
    ansi-html: private
  ansi-regex@2.1.1:
    ansi-regex: private
  ansi-styles@3.2.1:
    ansi-styles: private
  anymatch@2.0.0(supports-color@5.5.0):
    anymatch: private
  aproba@1.2.0:
    aproba: private
  argparse@1.0.10:
    argparse: private
  arr-diff@4.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-differ@3.0.0:
    array-differ: private
  array-find-index@1.0.2:
    array-find-index: private
  array-flatten@2.1.2:
    array-flatten: private
  array-includes@3.1.9:
    array-includes: private
  array-union@1.0.2:
    array-union: private
  array-uniq@1.0.3:
    array-uniq: private
  array-unique@0.3.2:
    array-unique: private
  array.prototype.reduce@1.0.8:
    array.prototype.reduce: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  arrify@2.0.1:
    arrify: private
  asn1.js@4.10.1:
    asn1.js: private
  assert@2.0.0:
    assert: private
  assign-symbols@1.0.0:
    assign-symbols: private
  async-each@1.0.6:
    async-each: private
  async-function@1.0.0:
    async-function: private
  async-limiter@1.0.1:
    async-limiter: private
  async-validator@1.8.5:
    async-validator: private
  async@2.6.4:
    async: private
  atob@2.1.2:
    atob: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  babel-code-frame@6.26.0:
    babel-code-frame: private
  babel-generator@6.26.1:
    babel-generator: private
  babel-helper-bindify-decorators@6.24.1:
    babel-helper-bindify-decorators: private
  babel-helper-builder-binary-assignment-operator-visitor@6.24.1:
    babel-helper-builder-binary-assignment-operator-visitor: private
  babel-helper-call-delegate@6.24.1:
    babel-helper-call-delegate: private
  babel-helper-define-map@6.26.0:
    babel-helper-define-map: private
  babel-helper-explode-assignable-expression@6.24.1:
    babel-helper-explode-assignable-expression: private
  babel-helper-explode-class@6.24.1:
    babel-helper-explode-class: private
  babel-helper-function-name@6.24.1:
    babel-helper-function-name: private
  babel-helper-get-function-arity@6.24.1:
    babel-helper-get-function-arity: private
  babel-helper-hoist-variables@6.24.1:
    babel-helper-hoist-variables: private
  babel-helper-optimise-call-expression@6.24.1:
    babel-helper-optimise-call-expression: private
  babel-helper-regex@6.26.0:
    babel-helper-regex: private
  babel-helper-remap-async-to-generator@6.24.1:
    babel-helper-remap-async-to-generator: private
  babel-helper-replace-supers@6.24.1:
    babel-helper-replace-supers: private
  babel-helpers@6.24.1:
    babel-helpers: private
  babel-messages@6.23.0:
    babel-messages: private
  babel-plugin-check-es2015-constants@6.22.0:
    babel-plugin-check-es2015-constants: private
  babel-plugin-syntax-async-functions@6.13.0:
    babel-plugin-syntax-async-functions: private
  babel-plugin-syntax-async-generators@6.13.0:
    babel-plugin-syntax-async-generators: private
  babel-plugin-syntax-class-properties@6.13.0:
    babel-plugin-syntax-class-properties: private
  babel-plugin-syntax-decorators@6.13.0:
    babel-plugin-syntax-decorators: private
  babel-plugin-syntax-dynamic-import@6.18.0:
    babel-plugin-syntax-dynamic-import: private
  babel-plugin-syntax-exponentiation-operator@6.13.0:
    babel-plugin-syntax-exponentiation-operator: private
  babel-plugin-syntax-object-rest-spread@6.13.0:
    babel-plugin-syntax-object-rest-spread: private
  babel-plugin-syntax-trailing-function-commas@6.22.0:
    babel-plugin-syntax-trailing-function-commas: private
  babel-plugin-transform-async-generator-functions@6.24.1:
    babel-plugin-transform-async-generator-functions: private
  babel-plugin-transform-async-to-generator@6.24.1:
    babel-plugin-transform-async-to-generator: private
  babel-plugin-transform-class-properties@6.24.1:
    babel-plugin-transform-class-properties: private
  babel-plugin-transform-decorators@6.24.1:
    babel-plugin-transform-decorators: private
  babel-plugin-transform-es2015-arrow-functions@6.22.0:
    babel-plugin-transform-es2015-arrow-functions: private
  babel-plugin-transform-es2015-block-scoped-functions@6.22.0:
    babel-plugin-transform-es2015-block-scoped-functions: private
  babel-plugin-transform-es2015-block-scoping@6.26.0:
    babel-plugin-transform-es2015-block-scoping: private
  babel-plugin-transform-es2015-classes@6.24.1:
    babel-plugin-transform-es2015-classes: private
  babel-plugin-transform-es2015-computed-properties@6.24.1:
    babel-plugin-transform-es2015-computed-properties: private
  babel-plugin-transform-es2015-destructuring@6.23.0:
    babel-plugin-transform-es2015-destructuring: private
  babel-plugin-transform-es2015-duplicate-keys@6.24.1:
    babel-plugin-transform-es2015-duplicate-keys: private
  babel-plugin-transform-es2015-for-of@6.23.0:
    babel-plugin-transform-es2015-for-of: private
  babel-plugin-transform-es2015-function-name@6.24.1:
    babel-plugin-transform-es2015-function-name: private
  babel-plugin-transform-es2015-literals@6.22.0:
    babel-plugin-transform-es2015-literals: private
  babel-plugin-transform-es2015-modules-amd@6.24.1:
    babel-plugin-transform-es2015-modules-amd: private
  babel-plugin-transform-es2015-modules-commonjs@6.26.2:
    babel-plugin-transform-es2015-modules-commonjs: private
  babel-plugin-transform-es2015-modules-systemjs@6.24.1:
    babel-plugin-transform-es2015-modules-systemjs: private
  babel-plugin-transform-es2015-modules-umd@6.24.1:
    babel-plugin-transform-es2015-modules-umd: private
  babel-plugin-transform-es2015-object-super@6.24.1:
    babel-plugin-transform-es2015-object-super: private
  babel-plugin-transform-es2015-parameters@6.24.1:
    babel-plugin-transform-es2015-parameters: private
  babel-plugin-transform-es2015-shorthand-properties@6.24.1:
    babel-plugin-transform-es2015-shorthand-properties: private
  babel-plugin-transform-es2015-spread@6.22.0:
    babel-plugin-transform-es2015-spread: private
  babel-plugin-transform-es2015-sticky-regex@6.24.1:
    babel-plugin-transform-es2015-sticky-regex: private
  babel-plugin-transform-es2015-template-literals@6.22.0:
    babel-plugin-transform-es2015-template-literals: private
  babel-plugin-transform-es2015-typeof-symbol@6.23.0:
    babel-plugin-transform-es2015-typeof-symbol: private
  babel-plugin-transform-es2015-unicode-regex@6.24.1:
    babel-plugin-transform-es2015-unicode-regex: private
  babel-plugin-transform-exponentiation-operator@6.24.1:
    babel-plugin-transform-exponentiation-operator: private
  babel-plugin-transform-object-rest-spread@6.26.0:
    babel-plugin-transform-object-rest-spread: private
  babel-plugin-transform-regenerator@6.26.0:
    babel-plugin-transform-regenerator: private
  babel-plugin-transform-strict-mode@6.24.1:
    babel-plugin-transform-strict-mode: private
  babel-preset-stage-3@6.24.1:
    babel-preset-stage-3: private
  babel-register@6.26.0:
    babel-register: private
  babel-runtime@6.26.0:
    babel-runtime: private
  babel-template@6.26.0:
    babel-template: private
  babel-traverse@6.26.0:
    babel-traverse: private
  babel-types@6.26.0:
    babel-types: private
  babylon@6.18.0:
    babylon: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  base@0.11.2:
    base: private
  batch@0.6.1:
    batch: private
  bfj-node4@5.3.1:
    bfj-node4: private
  big.js@3.2.0:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bluebird@3.7.2:
    bluebird: private
  bn.js@5.2.2:
    bn.js: private
  body-parser@1.20.3(supports-color@5.5.0):
    body-parser: private
  bonjour@3.5.0:
    bonjour: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@2.3.2(supports-color@5.5.0):
    braces: private
  brorand@1.1.0:
    brorand: private
  browserify-aes@1.2.0:
    browserify-aes: private
  browserify-cipher@1.0.1:
    browserify-cipher: private
  browserify-des@1.0.2:
    browserify-des: private
  browserify-rsa@4.1.1:
    browserify-rsa: private
  browserify-sign@4.2.3:
    browserify-sign: private
  browserify-zlib@0.2.0:
    browserify-zlib: private
  browserslist@2.11.3:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer-indexof@1.1.1:
    buffer-indexof: private
  buffer-xor@1.0.3:
    buffer-xor: private
  buffer@4.9.2:
    buffer: private
  builtin-status-codes@3.0.0:
    builtin-status-codes: private
  bytes@3.1.2:
    bytes: private
  cacache@10.0.4:
    cacache: private
  cache-base@1.0.1:
    cache-base: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  caller-callsite@2.0.0:
    caller-callsite: private
  caller-path@2.0.0:
    caller-path: private
  callsites@2.0.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  camelcase-keys@2.1.0:
    camelcase-keys: private
  camelcase@3.0.0:
    camelcase: private
  caniuse-api@1.6.1:
    caniuse-api: private
  caniuse-db@1.0.30001727:
    caniuse-db: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  center-align@0.1.3:
    center-align: private
  chance@1.1.9:
    chance: private
  char-regex@1.0.2:
    char-regex: private
  charenc@0.0.2:
    charenc: private
  check-types@7.4.0:
    check-types: private
  chokidar@2.1.8(supports-color@5.5.0):
    chokidar: private
  chownr@1.1.4:
    chownr: private
  cipher-base@1.0.6:
    cipher-base: private
  clap@1.2.3:
    clap: private
  class-utils@0.3.6:
    class-utils: private
  class-validator@0.14.1:
    class-validator: private
  clean-css@4.2.4:
    clean-css: private
  cli-cursor@2.1.0:
    cli-cursor: private
  cli-spinners@1.3.1:
    cli-spinners: private
  cliui@3.2.0:
    cliui: private
  clone@2.1.2:
    clone: private
  co@4.6.0:
    co: private
  coa@1.0.4:
    coa: private
  code-point-at@1.1.0:
    code-point-at: private
  collection-visit@1.0.0:
    collection-visit: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  color-string@0.3.0:
    color-string: private
  color@0.11.4:
    color: private
  colormin@1.1.2:
    colormin: private
  colors@1.1.2:
    colors: private
  commander@10.0.0:
    commander: private
  commondir@1.0.1:
    commondir: private
  component-emitter@1.3.1:
    component-emitter: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0(supports-color@5.5.0):
    compression: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@1.6.2:
    concat-stream: private
  connect-history-api-fallback@1.6.0:
    connect-history-api-fallback: private
  console-browserify@1.2.0:
    console-browserify: private
  consolidate@0.14.5(babel-core@6.26.3)(lodash@4.17.21):
    consolidate: private
  constants-browserify@1.0.0:
    constants-browserify: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@1.9.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  copy-anything@2.0.6:
    copy-anything: private
  copy-concurrently@1.0.5:
    copy-concurrently: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  core-js@2.6.12:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@5.2.1:
    cosmiconfig: private
  create-ecdh@4.0.4:
    create-ecdh: private
  create-hash@1.2.0:
    create-hash: private
  create-hmac@1.1.7:
    create-hmac: private
  cross-spawn@5.1.0:
    cross-spawn: private
  crypt@0.0.2:
    crypt: private
  crypto-browserify@3.12.1:
    crypto-browserify: private
  css-color-names@0.0.4:
    css-color-names: private
  css-declaration-sorter@4.0.1:
    css-declaration-sorter: private
  css-select-base-adapter@0.1.1:
    css-select-base-adapter: private
  css-select@4.3.0:
    css-select: private
  css-selector-tokenizer@0.7.3:
    css-selector-tokenizer: private
  css-tree@1.0.0-alpha.37:
    css-tree: private
  css-what@6.2.2:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  cssnano-preset-default@4.0.8:
    cssnano-preset-default: private
  cssnano-util-get-arguments@4.0.0:
    cssnano-util-get-arguments: private
  cssnano-util-get-match@4.0.0:
    cssnano-util-get-match: private
  cssnano-util-raw-cache@4.0.1:
    cssnano-util-raw-cache: private
  cssnano-util-same-parent@4.0.1:
    cssnano-util-same-parent: private
  cssnano@3.10.0:
    cssnano: private
  csso@2.3.2:
    csso: private
  csstype@3.1.3:
    csstype: private
  cuint@0.2.2:
    cuint: private
  currently-unhandled@0.4.1:
    currently-unhandled: private
  cyclist@1.0.2:
    cyclist: private
  d@1.0.2:
    d: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  de-indent@1.0.2:
    de-indent: private
  debug@2.6.9(supports-color@5.5.0):
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  deep-equal@1.1.2:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@1.5.2:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  define-property@1.0.0:
    define-property: private
  defined@1.0.1:
    defined: private
  del@3.0.0:
    del: private
  depd@2.0.0:
    depd: private
  des.js@1.1.0:
    des.js: private
  destroy@1.2.0:
    destroy: private
  detect-indent@4.0.0:
    detect-indent: private
  detect-node@2.1.0:
    detect-node: private
  diffie-hellman@5.0.3:
    diffie-hellman: private
  dir-glob@2.2.2:
    dir-glob: private
  dns-equal@1.0.0:
    dns-equal: private
  dns-packet@1.3.4:
    dns-packet: private
  dns-txt@2.0.2:
    dns-txt: private
  dom-converter@0.2.0:
    dom-converter: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domain-browser@1.2.0:
    domain-browser: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domready@1.0.8:
    domready: private
  domutils@2.8.0:
    domutils: private
  dot-prop@5.3.0:
    dot-prop: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  duplexify@3.7.1:
    duplexify: private
  ee-first@1.1.1:
    ee-first: private
  ejs@2.7.4:
    ejs: private
  electron-to-chromium@1.5.182:
    electron-to-chromium: private
  elliptic@6.6.1:
    elliptic: private
  emojis-list@2.1.0:
    emojis-list: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@3.4.1:
    enhanced-resolve: private
  entities@2.2.0:
    entities: private
  errno@0.1.8:
    errno: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-abstract@1.24.0:
    es-abstract: private
  es-array-method-boxes-properly@1.0.0:
    es-array-method-boxes-properly: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es5-ext@0.10.64:
    es5-ext: private
  es6-iterator@2.0.3:
    es6-iterator: private
  es6-map@0.1.5:
    es6-map: private
  es6-object-assign@1.1.0:
    es6-object-assign: private
  es6-set@0.1.6:
    es6-set: private
  es6-symbol@3.1.4:
    es6-symbol: private
  es6-weak-map@2.0.3:
    es6-weak-map: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  escope@3.6.0:
    escope: private
  eslint-scope@7.1.1:
    eslint-scope: private
  eslint-visitor-keys@3.3.0:
    eslint-visitor-keys: private
  esniff@2.0.1:
    esniff: private
  esprima@4.0.1:
    esprima: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-emitter@0.3.5:
    event-emitter: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  eventsource@0.1.6:
    eventsource: private
  evp_bytestokey@1.0.3:
    evp_bytestokey: private
  execa@0.7.0:
    execa: private
  expand-brackets@2.1.4(supports-color@5.5.0):
    expand-brackets: private
  express@4.21.2(supports-color@5.5.0):
    express: private
  ext@1.7.0:
    ext: private
  extend-shallow@2.0.1:
    extend-shallow: private
  extglob@2.0.4(supports-color@5.5.0):
    extglob: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastparse@1.1.2:
    fastparse: private
  faye-websocket@0.11.4:
    faye-websocket: private
  filesize@3.6.1:
    filesize: private
  fill-range@4.0.0:
    fill-range: private
  finalhandler@1.3.1(supports-color@5.5.0):
    finalhandler: private
  find-cache-dir@1.0.0:
    find-cache-dir: private
  find-up@2.1.0:
    find-up: private
  flatten@1.0.3:
    flatten: private
  flush-write-stream@1.1.1:
    flush-write-stream: private
  follow-redirects@1.15.9(debug@3.2.7(supports-color@5.5.0)):
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  for-in@1.0.2:
    for-in: private
  forwarded@0.2.0:
    forwarded: private
  fragment-cache@0.2.1:
    fragment-cache: private
  fresh@0.5.2:
    fresh: private
  from2@2.3.0:
    from2: private
  fs-write-stream-atomic@1.0.10:
    fs-write-stream-atomic: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@1.2.13:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-caller-file@1.0.3:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stdin@4.0.1:
    get-stdin: private
  get-stream@3.0.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-value@2.0.6:
    get-value: private
  glob-parent@3.1.0:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@9.18.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@7.1.1:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  growly@1.3.0:
    growly: private
  gzip-size@4.1.0:
    gzip-size: private
  handle-thing@2.0.1:
    handle-thing: private
  has-ansi@2.0.0:
    has-ansi: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@2.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  has@1.0.4:
    has: private
  hash-base@3.0.5:
    hash-base: private
  hash-sum@1.0.2:
    hash-sum: private
  hash.js@1.1.7:
    hash.js: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hex-color-regex@1.1.0:
    hex-color-regex: private
  hmac-drbg@1.0.1:
    hmac-drbg: private
  home-or-tmp@2.0.0:
    home-or-tmp: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  hpack.js@2.1.6:
    hpack.js: private
  hsl-regex@1.0.0:
    hsl-regex: private
  hsla-regex@1.0.0:
    hsla-regex: private
  html-comment-regex@1.1.2:
    html-comment-regex: private
  html-entities@1.4.0:
    html-entities: private
  html-minifier@3.5.21:
    html-minifier: private
  htmlparser2@6.1.0:
    htmlparser2: private
  http-deceiver@1.2.7:
    http-deceiver: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-middleware@0.19.2(debug@3.2.7(supports-color@5.5.0))(supports-color@5.5.0):
    http-proxy-middleware: private
  http-proxy@1.18.1(debug@3.2.7(supports-color@5.5.0)):
    http-proxy: private
  https-browserify@1.0.0:
    https-browserify: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-replace-symbols@1.1.0:
    icss-replace-symbols: private
  icss-utils@2.1.0:
    icss-utils: private
  ieee754@1.2.1:
    ieee754: private
  iferr@0.1.5:
    iferr: private
  ignore@3.3.10:
    ignore: private
  image-size@0.5.5:
    image-size: private
  import-cwd@2.1.0:
    import-cwd: private
  import-fresh@2.0.0:
    import-fresh: private
  import-from@2.1.0:
    import-from: private
  import-local@1.0.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@2.1.0:
    indent-string: private
  indexes-of@1.0.1:
    indexes-of: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-ip@1.2.0:
    internal-ip: private
  internal-slot@1.1.0:
    internal-slot: private
  interpret@1.4.0:
    interpret: private
  invariant@2.2.4:
    invariant: private
  inversify@6.0.1:
    inversify: private
  invert-kv@1.0.0:
    invert-kv: private
  ip@1.1.9:
    ip: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-absolute-url@2.1.0:
    is-absolute-url: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@1.0.1:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-color-stop@1.1.0:
    is-color-stop: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-descriptor@0.1.7:
    is-descriptor: private
  is-directory@0.3.1:
    is-directory: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-finite@1.1.0:
    is-finite: private
  is-fullwidth-code-point@2.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-nan@1.3.2:
    is-nan: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@3.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-path-cwd@1.0.0:
    is-path-cwd: private
  is-path-in-cwd@1.0.1:
    is-path-in-cwd: private
  is-path-inside@1.0.1:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-regex@1.2.1:
    is-regex: private
  is-resolvable@1.1.0:
    is-resolvable: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@1.1.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-svg@2.1.0:
    is-svg: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-utf8@0.2.1:
    is-utf8: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@3.14.1:
    is-what: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@1.1.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  js-base64@2.6.4:
    js-base64: private
  js-string-escape@1.0.1:
    js-string-escape: private
  js-tokens@3.0.2:
    js-tokens: private
  js-yaml@3.14.1:
    js-yaml: private
  jsesc@1.3.0:
    jsesc: private
  json-loader@0.5.7:
    json-loader: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json3@3.3.3:
    json3: private
  json5@0.5.1:
    json5: private
  killable@1.0.1:
    killable: private
  kind-of@5.1.0:
    kind-of: private
  last-call-webpack-plugin@2.1.2:
    last-call-webpack-plugin: private
  lazy-cache@1.0.4:
    lazy-cache: private
  lcid@1.0.0:
    lcid: private
  levn@0.3.0:
    levn: private
  libphonenumber-js@1.12.10:
    libphonenumber-js: private
  load-json-file@1.1.0:
    load-json-file: private
  loader-runner@2.4.0:
    loader-runner: private
  loader-utils@1.4.2:
    loader-utils: private
  locate-path@2.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-symbols@2.2.0:
    log-symbols: private
  loglevel@1.9.2:
    loglevel: private
  longest@1.0.1:
    longest: private
  loose-envify@1.4.0:
    loose-envify: private
  loud-rejection@1.6.0:
    loud-rejection: private
  lower-case@1.1.4:
    lower-case: private
  lru-cache@4.1.5:
    lru-cache: private
  make-dir@2.1.0:
    make-dir: private
  map-cache@0.2.2:
    map-cache: private
  map-obj@1.0.1:
    map-obj: private
  map-visit@1.0.0:
    map-visit: private
  math-expression-evaluator@1.4.0:
    math-expression-evaluator: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5.js@1.3.5:
    md5.js: private
  md5@2.3.0:
    md5: private
  mdn-data@2.0.4:
    mdn-data: private
  media-typer@0.3.0:
    media-typer: private
  mem@1.1.0:
    mem: private
  memory-fs@0.4.1:
    memory-fs: private
  meow@3.7.0:
    meow: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-options@1.0.1:
    merge-options: private
  methods@1.1.2:
    methods: private
  micromatch@3.1.10(supports-color@5.5.0):
    micromatch: private
  miller-rabin@4.0.1:
    miller-rabin: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@1.2.0:
    mimic-fn: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimalistic-crypto-utils@1.0.1:
    minimalistic-crypto-utils: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mississippi@2.0.0:
    mississippi: private
  mitt@1.1.2:
    mitt: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mkdirp@0.5.6:
    mkdirp: private
  move-concurrently@1.0.1:
    move-concurrently: private
  ms@2.0.0:
    ms: private
  multicast-dns-service-types@1.1.0:
    multicast-dns-service-types: private
  multicast-dns@6.2.3:
    multicast-dns: private
  multimatch@5.0.0:
    multimatch: private
  nanoid@3.3.11:
    nanoid: private
  nanomatch@1.2.13(supports-color@5.5.0):
    nanomatch: private
  native-request@1.1.2:
    native-request: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  next-tick@1.1.0:
    next-tick: private
  no-case@2.3.2:
    no-case: private
  node-forge@0.10.0:
    node-forge: private
  node-libs-browser@2.2.1:
    node-libs-browser: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@1.9.1:
    normalize-url: private
  normalize-wheel@1.0.1:
    normalize-wheel: private
  npm-run-path@2.0.2:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  num2fraction@1.2.2:
    num2fraction: private
  number-is-nan@1.0.1:
    number-is-nan: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object-visit@1.0.1:
    object-visit: private
  object.assign@4.1.7:
    object.assign: private
  object.getownpropertydescriptors@2.1.8:
    object.getownpropertydescriptors: private
  object.pick@1.3.0:
    object.pick: private
  object.values@1.2.1:
    object.values: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@2.0.1:
    onetime: private
  opencollective-postinstall@2.0.3:
    opencollective-postinstall: private
  opener@1.5.2:
    opener: private
  opn@5.5.0:
    opn: private
  optionator@0.8.3:
    optionator: private
  original@1.0.2:
    original: private
  os-browserify@0.3.0:
    os-browserify: private
  os-homedir@1.0.2:
    os-homedir: private
  os-locale@1.4.0:
    os-locale: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@1.3.0:
    p-limit: private
  p-locate@2.0.0:
    p-locate: private
  p-map@1.2.0:
    p-map: private
  p-try@1.0.0:
    p-try: private
  pako@1.0.11:
    pako: private
  parallel-transform@1.2.0:
    parallel-transform: private
  param-case@2.1.1:
    param-case: private
  parse-asn1@5.1.7:
    parse-asn1: private
  parse-json@2.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  pascalcase@0.1.1:
    pascalcase: private
  path-browserify@0.0.1:
    path-browserify: private
  path-dirname@1.0.2:
    path-dirname: private
  path-exists@2.1.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-is-inside@1.0.2:
    path-is-inside: private
  path-key@2.0.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@3.0.0:
    path-type: private
  pbkdf2@3.1.3:
    pbkdf2: private
  picocolors@0.2.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@4.0.1:
    pify: private
  pinkie-promise@2.0.1:
    pinkie-promise: private
  pinkie@2.0.4:
    pinkie: private
  pkg-dir@2.0.0:
    pkg-dir: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-calc@5.3.1:
    postcss-calc: private
  postcss-colormin@2.2.2:
    postcss-colormin: private
  postcss-convert-values@2.6.1:
    postcss-convert-values: private
  postcss-discard-comments@2.0.4:
    postcss-discard-comments: private
  postcss-discard-duplicates@2.1.0:
    postcss-discard-duplicates: private
  postcss-discard-empty@2.1.0:
    postcss-discard-empty: private
  postcss-discard-overridden@0.1.1:
    postcss-discard-overridden: private
  postcss-discard-unused@2.2.3:
    postcss-discard-unused: private
  postcss-filter-plugins@2.0.3:
    postcss-filter-plugins: private
  postcss-load-config@2.1.2:
    postcss-load-config: private
  postcss-load-options@1.2.0:
    postcss-load-options: private
  postcss-load-plugins@2.3.0:
    postcss-load-plugins: private
  postcss-merge-idents@2.1.7:
    postcss-merge-idents: private
  postcss-merge-longhand@2.0.2:
    postcss-merge-longhand: private
  postcss-merge-rules@2.1.2:
    postcss-merge-rules: private
  postcss-message-helpers@2.0.0:
    postcss-message-helpers: private
  postcss-minify-font-values@1.0.5:
    postcss-minify-font-values: private
  postcss-minify-gradients@1.0.5:
    postcss-minify-gradients: private
  postcss-minify-params@1.2.2:
    postcss-minify-params: private
  postcss-minify-selectors@2.1.1:
    postcss-minify-selectors: private
  postcss-modules-extract-imports@1.2.1:
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@1.2.0:
    postcss-modules-local-by-default: private
  postcss-modules-scope@1.1.0:
    postcss-modules-scope: private
  postcss-modules-values@1.3.0:
    postcss-modules-values: private
  postcss-normalize-charset@1.1.1:
    postcss-normalize-charset: private
  postcss-normalize-display-values@4.0.2:
    postcss-normalize-display-values: private
  postcss-normalize-positions@4.0.2:
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@4.0.2:
    postcss-normalize-repeat-style: private
  postcss-normalize-string@4.0.2:
    postcss-normalize-string: private
  postcss-normalize-timing-functions@4.0.2:
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@4.0.1:
    postcss-normalize-unicode: private
  postcss-normalize-url@3.0.8:
    postcss-normalize-url: private
  postcss-normalize-whitespace@4.0.2:
    postcss-normalize-whitespace: private
  postcss-ordered-values@2.2.3:
    postcss-ordered-values: private
  postcss-prefix-selector@1.16.1(postcss@5.2.18):
    postcss-prefix-selector: private
  postcss-reduce-idents@2.4.0:
    postcss-reduce-idents: private
  postcss-reduce-initial@1.0.1:
    postcss-reduce-initial: private
  postcss-reduce-transforms@1.0.4:
    postcss-reduce-transforms: private
  postcss-selector-parser@2.2.3:
    postcss-selector-parser: private
  postcss-svgo@2.1.6:
    postcss-svgo: private
  postcss-unique-selectors@2.0.2:
    postcss-unique-selectors: private
  postcss-value-parser@3.3.1:
    postcss-value-parser: private
  postcss-zindex@2.2.0:
    postcss-zindex: private
  postcss@6.0.23:
    postcss: private
  posthtml-parser@0.2.1:
    posthtml-parser: private
  posthtml-rename-id@1.0.12:
    posthtml-rename-id: private
  posthtml-render@1.4.0:
    posthtml-render: private
  posthtml-svg-mode@1.0.3:
    posthtml-svg-mode: private
  posthtml@0.9.2:
    posthtml: private
  prelude-ls@1.1.2:
    prelude-ls: private
  prepend-http@1.0.4:
    prepend-http: private
  prettier@1.19.1:
    prettier: private
  pretty-error@2.1.2:
    pretty-error: private
  private@0.1.8:
    private: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  promise-inflight@1.0.1(bluebird@3.7.2):
    promise-inflight: private
  proxy-addr@2.0.7:
    proxy-addr: private
  prr@1.0.1:
    prr: private
  pseudomap@1.0.2:
    pseudomap: private
  public-encrypt@4.0.3:
    public-encrypt: private
  pump@2.0.1:
    pump: private
  pumpify@1.5.1:
    pumpify: private
  punycode@1.4.1:
    punycode: private
  q@1.5.1:
    q: private
  qs@6.13.0:
    qs: private
  query-string@4.3.4:
    query-string: private
  querystring-es3@0.2.1:
    querystring-es3: private
  querystringify@2.2.0:
    querystringify: private
  randombytes@2.1.0:
    randombytes: private
  randomfill@1.0.4:
    randomfill: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  read-cache@1.0.0:
    read-cache: private
  read-pkg-up@1.0.1:
    read-pkg-up: private
  read-pkg@1.1.0:
    read-pkg: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@2.2.1(supports-color@5.5.0):
    readdirp: private
  rechoir@0.6.2:
    rechoir: private
  redent@1.0.0:
    redent: private
  reduce-css-calc@1.3.0:
    reduce-css-calc: private
  reduce-function-call@1.0.3:
    reduce-function-call: private
  reflect-metadata@0.1.13:
    reflect-metadata: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.11.1:
    regenerator-runtime: private
  regenerator-transform@0.10.1:
    regenerator-transform: private
  regex-not@1.0.2:
    regex-not: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@2.0.0:
    regexpu-core: private
  regjsgen@0.2.0:
    regjsgen: private
  regjsparser@0.1.5:
    regjsparser: private
  relateurl@0.2.7:
    relateurl: private
  remove-trailing-separator@1.1.0:
    remove-trailing-separator: private
  renderkid@2.0.7:
    renderkid: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  repeating@2.0.1:
    repeating: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@1.2.1:
    require-from-string: private
  require-main-filename@1.0.1:
    require-main-filename: private
  requires-port@1.0.0:
    requires-port: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-cwd@2.0.0:
    resolve-cwd: private
  resolve-from@3.0.0:
    resolve-from: private
  resolve-url@0.2.1:
    resolve-url: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@2.0.0:
    restore-cursor: private
  ret@0.1.15:
    ret: private
  rgb-regex@1.0.1:
    rgb-regex: private
  rgba-regex@1.0.0:
    rgba-regex: private
  right-align@0.1.3:
    right-align: private
  ripemd160@2.0.2:
    ripemd160: private
  run-queue@1.0.3:
    run-queue: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.2.4:
    sax: private
  schema-utils@0.3.0:
    schema-utils: private
  select-hose@2.0.0:
    select-hose: private
  selfsigned@1.10.14:
    selfsigned: private
  send@0.19.0(supports-color@5.5.0):
    send: private
  serialize-javascript@1.9.1:
    serialize-javascript: private
  serve-index@1.9.1(supports-color@5.5.0):
    serve-index: private
  serve-static@1.16.2(supports-color@5.5.0):
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  set-value@2.0.1:
    set-value: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.12:
    sha.js: private
  shebang-command@1.2.0:
    shebang-command: private
  shebang-regex@1.0.0:
    shebang-regex: private
  shellwords@0.1.1:
    shellwords: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slash@1.0.0:
    slash: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2(supports-color@5.5.0):
    snapdragon: private
  sockjs-client@1.1.5(supports-color@5.5.0):
    sockjs-client: private
  sockjs@0.3.19:
    sockjs: private
  sort-keys@1.1.2:
    sort-keys: private
  source-list-map@2.0.1:
    source-list-map: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.5.7:
    source-map: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  spdy-transport@3.0.0(supports-color@5.5.0):
    spdy-transport: private
  spdy@4.0.2(supports-color@5.5.0):
    spdy: private
  split-string@3.1.0:
    split-string: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssri@5.3.0:
    ssri: private
  stable@0.1.8:
    stable: private
  stackframe@1.3.4:
    stackframe: private
  static-extend@0.1.2:
    static-extend: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-browserify@2.0.2:
    stream-browserify: private
  stream-each@1.2.3:
    stream-each: private
  stream-http@2.8.3:
    stream-http: private
  stream-shift@1.0.3:
    stream-shift: private
  strict-uri-encode@1.1.0:
    strict-uri-encode: private
  string-template@1.0.0:
    string-template: private
  string-width@2.1.1:
    string-width: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringz@2.1.0:
    stringz: private
  strip-ansi@3.0.1:
    strip-ansi: private
  strip-bom@2.0.0:
    strip-bom: private
  strip-eof@1.0.0:
    strip-eof: private
  strip-indent@1.0.1:
    strip-indent: private
  stylehacks@4.0.3:
    stylehacks: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-baker-runtime@1.4.7:
    svg-baker-runtime: private
  svg-baker@1.7.0:
    svg-baker: private
  svgo@0.7.2:
    svgo: private
  tapable@0.2.9:
    tapable: private
  throttle-debounce@1.1.0:
    throttle-debounce: private
  through2@2.0.5:
    through2: private
  thunky@1.1.0:
    thunky: private
  time-stamp@2.2.0:
    time-stamp: private
  timers-browserify@2.0.12:
    timers-browserify: private
  timsort@0.3.0:
    timsort: private
  to-arraybuffer@1.0.1:
    to-arraybuffer: private
  to-buffer@1.2.1:
    to-buffer: private
  to-fast-properties@1.0.3:
    to-fast-properties: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@5.0.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  toidentifier@1.0.1:
    toidentifier: private
  toposort@1.0.7:
    toposort: private
  traverse@0.6.11:
    traverse: private
  trim-newlines@1.0.0:
    trim-newlines: private
  trim-right@1.0.1:
    trim-right: private
  tryer@1.0.1:
    tryer: private
  tslib@2.5.0:
    tslib: private
  tty-browserify@0.0.0:
    tty-browserify: private
  type-check@0.3.2:
    type-check: private
  type-is@1.6.18:
    type-is: private
  type@2.7.3:
    type: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray.prototype.slice@1.0.5:
    typedarray.prototype.slice: private
  typedarray@0.0.6:
    typedarray: private
  uglify-es@3.3.9:
    uglify-es: private
  uglify-js@3.4.10:
    uglify-js: private
  uglify-to-browserify@1.0.2:
    uglify-to-browserify: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unidecode@0.1.8:
    unidecode: private
  union-value@1.0.1:
    union-value: private
  uniq@1.0.1:
    uniq: private
  uniqs@2.0.0:
    uniqs: private
  unique-filename@1.1.1:
    unique-filename: private
  unique-slug@2.0.2:
    unique-slug: private
  unpipe@1.0.0:
    unpipe: private
  unquote@1.1.1:
    unquote: private
  unset-value@1.0.0:
    unset-value: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  upper-case@1.1.3:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  urix@0.1.0:
    urix: private
  url-parse@1.5.10:
    url-parse: private
  url-slug@2.0.0:
    url-slug: private
  url@0.11.4:
    url: private
  use@3.1.1:
    use: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util.promisify@1.0.1:
    util.promisify: private
  util@0.12.5:
    util: private
  utila@0.4.0:
    utila: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@3.4.0:
    uuid: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  vendors@1.0.4:
    vendors: private
  vm-browserify@1.1.2:
    vm-browserify: private
  vue-hot-reload-api@2.3.4:
    vue-hot-reload-api: private
  vue-template-es2015-compiler@1.9.1:
    vue-template-es2015-compiler: private
  watchpack-chokidar2@2.0.1(supports-color@4.5.0):
    watchpack-chokidar2: private
  watchpack@1.7.5(supports-color@4.5.0):
    watchpack: private
  wbuf@1.7.3:
    wbuf: private
  webpack-dev-middleware@1.12.2(webpack@3.12.0):
    webpack-dev-middleware: private
  webpack-sources@1.4.3:
    webpack-sources: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  whet.extend@0.9.9:
    whet.extend: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@1.0.0:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@1.3.1:
    which: private
  window-size@0.1.0:
    window-size: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@0.0.2:
    wordwrap: private
  worker-farm@1.7.0:
    worker-farm: private
  wrap-ansi@2.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@4.1.0:
    ws: private
  xtend@4.0.2:
    xtend: private
  xxhashjs@0.2.2:
    xxhashjs: private
  y18n@4.0.3:
    y18n: private
  yallist@2.1.2:
    yallist: private
  yargs-parser@4.2.1:
    yargs-parser: private
  yargs@6.6.0:
    yargs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Mon, 14 Jul 2025 06:00:01 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - bindings@1.5.0
  - file-uri-to-path@1.0.0
  - fsevents@1.2.13
  - fsevents@2.3.3
  - nan@2.23.0
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm
virtualStoreDirMaxLength: 60

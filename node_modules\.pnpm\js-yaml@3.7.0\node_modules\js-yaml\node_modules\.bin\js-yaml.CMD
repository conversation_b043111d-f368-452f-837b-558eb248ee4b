@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\js-yaml@3.7.0\node_modules\js-yaml\bin\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\js-yaml@3.7.0\node_modules\js-yaml\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\js-yaml@3.7.0\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\js-yaml@3.7.0\node_modules\js-yaml\bin\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\js-yaml@3.7.0\node_modules\js-yaml\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\js-yaml@3.7.0\node_modules;C:\workspaces\zjh\dc\sprite\miniapp-jssdk-demo\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\js-yaml.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\js-yaml.js" %*
)

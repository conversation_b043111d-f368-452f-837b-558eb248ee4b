#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/webpack-dev-server@2.11.5_webpack@3.12.0/node_modules/webpack-dev-server/bin/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/webpack-dev-server@2.11.5_webpack@3.12.0/node_modules/webpack-dev-server/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/webpack-dev-server@2.11.5_webpack@3.12.0/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/webpack-dev-server@2.11.5_webpack@3.12.0/node_modules/webpack-dev-server/bin/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/webpack-dev-server@2.11.5_webpack@3.12.0/node_modules/webpack-dev-server/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/webpack-dev-server@2.11.5_webpack@3.12.0/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/webpack-dev-server.js" "$@"
else
  exec node  "$basedir/../../bin/webpack-dev-server.js" "$@"
fi

<template>
  <div class="sprite-container">
    <div class="sprite-header">
      <h1 class="title">选择你的精灵</h1>
      <p class="subtitle">选择一只精灵陪伴你的通话时光</p>
    </div>

    <div class="sprite-card">
      <div class="card-title">可用精灵</div>
      
      <div class="sprite-selection-grid">
        <div 
          v-for="spriteType in spriteTypes" 
          :key="spriteType.id"
          class="sprite-option"
          :class="{
            'selected': selectedSpriteType === spriteType.id,
            'locked': !isSpriteUnlocked(spriteType.id),
            'owned': isSpritOwned(spriteType.id),
            'current': isCurrentSprite(spriteType.id)
          }"
          @click="handleSpriteSelect(spriteType)"
        >
          <!-- 锁定状态 -->
          <div v-if="!isSpriteUnlocked(spriteType.id)" class="lock-overlay">
            <i class="el-icon-lock lock-icon"></i>
            <p class="unlock-requirement">
              需要累计通话 {{ formatDuration(spriteType.unlockRequirement) }}
            </p>
            <p class="current-progress">
              当前进度: {{ formatDuration(user.totalCallTime) }}
            </p>
          </div>

          <!-- 精灵预览 -->
          <div v-else class="sprite-content">
            <img 
              :src="getSpriteImage(spriteType.id, 1)" 
              :alt="spriteType.name"
              class="sprite-preview"
              @error="handleImageError"
            />
            
            <h3 class="sprite-name">{{ spriteType.name }}</h3>
            
            <!-- 状态标识 -->
            <div class="status-badges">
              <span v-if="isCurrentSprite(spriteType.id)" class="badge current-badge">
                当前携带
              </span>
              <span v-else-if="isSpritOwned(spriteType.id)" class="badge owned-badge">
                已拥有
              </span>
              <span v-else class="badge available-badge">
                可获得
              </span>
            </div>

            <!-- 精灵信息 -->
            <div class="sprite-info">
              <p class="unlock-info">
                解锁条件: {{ spriteType.unlockRequirement === 0 ? '初始精灵' : `通话${formatDuration(spriteType.unlockRequirement)}` }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button 
        v-if="selectedSpriteType && !isCurrentSprite(selectedSpriteType)"
        type="primary" 
        size="large"
        :loading="isProcessing"
        @click="handleConfirmSelection"
      >
        {{ isSpritOwned(selectedSpriteType) ? '切换到此精灵' : '获得此精灵' }}
      </el-button>
      
      <el-button 
        size="large"
        @click="goBack"
      >
        返回
      </el-button>
    </div>

    <!-- 精灵详情弹窗 -->
    <el-dialog
      title="精灵详情"
      :visible.sync="showSpriteDetail"
      width="80%"
      center
    >
      <div v-if="selectedSpriteDetail" class="sprite-detail-content">
        <div class="detail-header">
          <img 
            :src="getSpriteImage(selectedSpriteDetail.id, 1)" 
            :alt="selectedSpriteDetail.name"
            class="detail-avatar"
          />
          <h2>{{ selectedSpriteDetail.name }}</h2>
        </div>
        
        <div class="detail-forms">
          <h3>成长形态</h3>
          <div class="forms-grid">
            <div 
              v-for="(form, formId) in selectedSpriteDetail.forms" 
              :key="formId"
              class="form-item"
            >
              <img 
                :src="getSpriteImage(selectedSpriteDetail.id, formId)" 
                :alt="`${selectedSpriteDetail.name} 形态${formId}`"
                class="form-image"
              />
              <p class="form-level">{{ form.level[0] }}-{{ form.level[1] }}级</p>
            </div>
          </div>
        </div>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="showSpriteDetail = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import { formatDuration } from '@/utils/spriteCalculator'

export default {
  name: 'SpriteSelectionPage',
  data() {
    return {
      selectedSpriteType: null,
      isProcessing: false,
      showSpriteDetail: false,
      selectedSpriteDetail: null
    }
  },
  computed: {
    ...mapState('sprite', ['user', 'sprites', 'spriteTypes']),
    ...mapGetters('sprite', ['unlockedSpriteTypes'])
  },
  mounted() {
    // 设置当前携带的精灵为选中状态
    if (this.user.currentSpriteId) {
      const currentSprite = this.sprites[this.user.currentSpriteId]
      if (currentSprite) {
        this.selectedSpriteType = currentSprite.typeId
      }
    }
  },
  methods: {
    ...mapActions('sprite', ['claimSprite', 'switchSprite']),

    /**
     * 检查精灵是否已解锁
     */
    isSpriteUnlocked(spriteTypeId) {
      return this.user.totalCallTime >= this.getSpriteType(spriteTypeId).unlockRequirement
    },

    /**
     * 检查精灵是否已拥有
     */
    isSpritOwned(spriteTypeId) {
      return Object.values(this.sprites).some(sprite => sprite.typeId === spriteTypeId)
    },

    /**
     * 检查是否为当前精灵
     */
    isCurrentSprite(spriteTypeId) {
      if (!this.user.currentSpriteId) return false
      const currentSprite = this.sprites[this.user.currentSpriteId]
      return currentSprite && currentSprite.typeId === spriteTypeId
    },

    /**
     * 获取精灵类型信息
     */
    getSpriteType(spriteTypeId) {
      return this.spriteTypes.find(type => type.id === spriteTypeId) || {}
    },

    /**
     * 获取精灵图片
     */
    getSpriteImage(spriteTypeId, form = 1) {
      try {
        return require(`@/assets/images/sprites/sprite${spriteTypeId}_form${form}.png`)
      } catch (error) {
        return require('@/assets/images/sprites/placeholder.svg')
      }
    },

    /**
     * 处理精灵选择
     */
    handleSpriteSelect(spriteType) {
      if (!this.isSpriteUnlocked(spriteType.id)) {
        this.$message.warning('此精灵尚未解锁')
        return
      }

      this.selectedSpriteType = spriteType.id
      
      // 双击显示详情
      if (this.selectedSpriteDetail && this.selectedSpriteDetail.id === spriteType.id) {
        this.showSpriteDetail = true
      } else {
        this.selectedSpriteDetail = spriteType
      }
    },

    /**
     * 确认选择
     */
    async handleConfirmSelection() {
      if (!this.selectedSpriteType) return

      this.isProcessing = true

      try {
        if (this.isSpritOwned(this.selectedSpriteType)) {
          // 切换到已有精灵
          const sprite = Object.values(this.sprites).find(s => s.typeId === this.selectedSpriteType)
          await this.switchSprite(sprite.id)
          this.$message.success('精灵切换成功！')
        } else {
          // 获得新精灵
          await this.claimSprite(this.selectedSpriteType)
          this.$message.success('恭喜获得新精灵！')
        }

        // 返回互动页面
        this.$router.push('/sprite-interaction')

      } catch (error) {
        console.error('精灵选择失败:', error)
        this.$message.error('操作失败，请重试')
      } finally {
        this.isProcessing = false
      }
    },

    /**
     * 返回上一页
     */
    goBack() {
      this.$router.go(-1)
    },

    /**
     * 格式化时长
     */
    formatDuration(minutes) {
      return formatDuration(minutes)
    },

    /**
     * 处理图片加载错误
     */
    handleImageError(event) {
      event.target.src = require('@/assets/images/sprites/placeholder.svg')
    }
  }
}
</script>

<style lang="less" scoped>
@import '../styles/sprite.less';

.sprite-selection-grid {
  .sprite-option {
    position: relative;
    min-height: 200px;
    
    &.locked {
      .lock-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 12px;
        
        .lock-icon {
          font-size: 32px;
          color: #999;
          margin-bottom: 12px;
        }
        
        .unlock-requirement {
          font-size: 12px;
          color: #666;
          text-align: center;
          margin-bottom: 8px;
        }
        
        .current-progress {
          font-size: 11px;
          color: #999;
          text-align: center;
        }
      }
    }
    
    &.current {
      border-color: #67c23a;
      background-color: #f0f9ff;
      
      .current-badge {
        background-color: #67c23a;
      }
    }
    
    .sprite-content {
      .sprite-preview {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0 auto 12px;
        display: block;
      }
      
      .sprite-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }
      
      .status-badges {
        margin-bottom: 12px;
        
        .badge {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 10px;
          color: white;
          
          &.current-badge {
            background-color: #67c23a;
          }
          
          &.owned-badge {
            background-color: #409eff;
          }
          
          &.available-badge {
            background-color: #909399;
          }
        }
      }
      
      .sprite-info {
        .unlock-info {
          font-size: 11px;
          color: #999;
          margin: 0;
        }
      }
    }
  }
}

.action-buttons {
  text-align: center;
  margin-top: 24px;
  
  .el-button {
    margin: 0 8px;
    min-width: 120px;
  }
}

.sprite-detail-content {
  .detail-header {
    text-align: center;
    margin-bottom: 24px;
    
    .detail-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin-bottom: 12px;
    }
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .detail-forms {
    h3 {
      color: #333;
      margin-bottom: 16px;
    }
    
    .forms-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      
      .form-item {
        text-align: center;
        
        .form-image {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          margin-bottom: 8px;
        }
        
        .form-level {
          font-size: 12px;
          color: #666;
          margin: 0;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .sprite-selection-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-forms .forms-grid {
    grid-template-columns: 1fr;
  }
}
</style>

webpackJsonp([3],{FXP8:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("s/F+"),i=r.n(n),s=r("yrbl"),a=r.n(s),c=r("vvmr"),u=r.n(c),o=r("nVSy"),l=r("AK6W"),p={name:"SpriteCheckPage",computed:u()({},Object(o.d)("sprite",["user"])),mounted:function(){var e=this;return a()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.checkSpriteStatus();case 2:case"end":return t.stop()}},t,e)}))()},methods:u()({},Object(o.b)("sprite",["initializeSpriteData"]),{checkSpriteStatus:function(){var e=this;return a()(i.a.mark(function t(){var r;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.getPlugins();case 3:return r=t.sent,l.default.init(r),t.next=7,e.initializeSpriteData();case 7:e.user.hasSprite&&e.user.currentSpriteId?(console.log("用户已认领精灵，跳转到互动页面"),e.$router.replace("/sprite-interaction")):(console.log("用户未认领精灵，跳转到认领页面"),e.$router.replace("/sprite-claim")),t.next=15;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("检查精灵状态失败:",t.t0),e.$message.error("初始化失败，请重试"),setTimeout(function(){e.$router.replace("/sprite-claim")},2e3);case 15:case"end":return t.stop()}},t,e,[[0,10]])}))()},getPlugins:function(){var e=this;return a()(i.a.mark(function t(){var n,s;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!window.plugins){t.next=2;break}return t.abrupt("return",window.plugins);case 2:if(!e.$root.$children||!e.$root.$children[0]){t.next=6;break}if(!(n=e.$root.$children[0]).plugins){t.next=6;break}return t.abrupt("return",n.plugins);case 6:return t.prev=6,t.next=9,new Promise(function(e){e()}).then(r.bind(null,"4nF9"));case 9:return s=t.sent,t.abrupt("return",s.default);case 13:throw t.prev=13,t.t0=t.catch(6),console.error("无法获取plugins对象:",t.t0),new Error("插件初始化失败");case 17:case"end":return t.stop()}},t,e,[[6,13]])}))()}})},d={render:function(){this.$createElement;this._self._c;return this._m(0)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"sprite-container"},[t("div",{staticClass:"check-content"},[t("div",{staticClass:"loading-animation"},[t("div",{staticClass:"loading-spinner"}),this._v(" "),t("h2",{staticClass:"loading-title"},[this._v("正在检查精灵状态...")]),this._v(" "),t("p",{staticClass:"loading-subtitle"},[this._v("请稍候")])])])])}]};var h=r("l2X3")(p,d,!1,function(e){r("YsBX")},"data-v-67ea5336",null);t.default=h.exports},YsBX:function(e,t){}});
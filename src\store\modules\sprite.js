import Vue from 'vue'

// 精灵状态管理模块
const state = {
  // 用户相关状态
  user: {
    hasSprite: false,           // 是否已认领精灵
    currentSpriteId: null,      // 当前携带的精灵ID
    unlockedSprites: [],        // 已解锁的精灵列表
    totalCallTime: 0,           // 累计通话时长（分钟）
    consecutiveCallDays: 0,     // 连续通话天数
    lastCallDate: null,         // 最后通话日期
    lastFeedDate: null,         // 最后投喂日期
  },
  
  // 精灵实例数据
  sprites: {},
  
  // 精灵类型配置
  spriteTypes: [
    {
      id: 1,
      name: '精灵类型1',
      unlockRequirement: 0,    // 解锁所需通话时长（分钟）
      forms: {
        1: { level: [1, 5], image: 'sprite1_form1.png' },
        2: { level: [6, 15], image: 'sprite1_form2.png' },
        3: { level: [16, 99], image: 'sprite1_form3.png' }
      }
    },
    {
      id: 2,
      name: '精灵类型2',
      unlockRequirement: 60,   // 1小时解锁
      forms: {
        1: { level: [1, 5], image: 'sprite2_form1.png' },
        2: { level: [6, 15], image: 'sprite2_form2.png' },
        3: { level: [16, 99], image: 'sprite2_form3.png' }
      }
    }
  ],
  
  // 通话状态
  callState: {
    isInCall: false,
    callStartTime: null,
    currentCallDuration: 0
  },

  // 通话时长更新定时器
  callDurationTimer: null
}

const getters = {
  // 获取当前精灵
  currentSprite: (state) => {
    return state.user.currentSpriteId ? state.sprites[state.user.currentSpriteId] : null
  },
  
  // 获取当前精灵等级
  currentSpriteLevel: (state, getters) => {
    const sprite = getters.currentSprite
    return sprite ? Math.floor(sprite.experience / 50) + 1 : 1
  },
  
  // 获取当前精灵形态
  currentSpriteForm: (state, getters) => {
    const level = getters.currentSpriteLevel
    if (level <= 5) return 1
    if (level <= 15) return 2
    return 3
  },
  
  // 获取当前经验值进度
  experienceProgress: (state, getters) => {
    const sprite = getters.currentSprite
    if (!sprite) return { current: 0, needed: 50, percentage: 0 }
    
    const currentLevelExp = sprite.experience % 50
    const neededExp = 50 - currentLevelExp
    const percentage = (currentLevelExp / 50) * 100
    
    return {
      current: currentLevelExp,
      needed: neededExp,
      percentage: percentage
    }
  },
  
  // 获取已解锁的精灵类型
  unlockedSpriteTypes: (state) => {
    return state.spriteTypes.filter(type => 
      state.user.totalCallTime >= type.unlockRequirement
    )
  },
  
  // 检查是否有闪光特效
  hasGlowEffect: (state) => {
    return state.user.consecutiveCallDays >= 3
  }
}

const mutations = {
  // 设置用户精灵状态
  SET_USER_SPRITE_STATUS(state, hasSprite) {
    state.user.hasSprite = hasSprite
  },
  
  // 设置当前精灵
  SET_CURRENT_SPRITE(state, spriteId) {
    state.user.currentSpriteId = spriteId
  },
  
  // 添加精灵实例
  ADD_SPRITE(state, sprite) {
    Vue.set(state.sprites, sprite.id, sprite)
  },
  
  // 更新精灵经验值
  UPDATE_SPRITE_EXPERIENCE(state, { spriteId, experience }) {
    if (state.sprites[spriteId]) {
      state.sprites[spriteId].experience = experience
    }
  },
  
  // 更新精灵名字
  UPDATE_SPRITE_NAME(state, { spriteId, name }) {
    if (state.sprites[spriteId]) {
      state.sprites[spriteId].name = name
    }
  },
  
  // 更新累计通话时长
  UPDATE_TOTAL_CALL_TIME(state, minutes) {
    state.user.totalCallTime += minutes
  },
  
  // 设置通话状态
  SET_CALL_STATE(state, { isInCall, startTime = null }) {
    state.callState.isInCall = isInCall
    state.callState.callStartTime = startTime
    if (!isInCall) {
      state.callState.currentCallDuration = 0
    }
  },
  
  // 更新当前通话时长
  UPDATE_CURRENT_CALL_DURATION(state, duration) {
    state.callState.currentCallDuration = duration
  },
  
  // 更新连续通话天数
  UPDATE_CONSECUTIVE_CALL_DAYS(state, days) {
    state.user.consecutiveCallDays = days
  },
  
  // 设置最后通话日期
  SET_LAST_CALL_DATE(state, date) {
    state.user.lastCallDate = date
  },
  
  // 设置最后投喂日期
  SET_LAST_FEED_DATE(state, date) {
    state.user.lastFeedDate = date
  },
  
  // 添加解锁的精灵
  ADD_UNLOCKED_SPRITE(state, spriteTypeId) {
    if (!state.user.unlockedSprites.includes(spriteTypeId)) {
      state.user.unlockedSprites.push(spriteTypeId)
    }
  },
  
  // 设置通话时长定时器
  SET_CALL_DURATION_TIMER(state, timer) {
    state.callDurationTimer = timer
  },

  // 加载完整状态
  LOAD_STATE(state, loadedState) {
    Object.assign(state, loadedState)
  }
}

const actions = {
  // 初始化精灵数据
  async initializeSpriteData({ commit, dispatch }) {
    try {
      await dispatch('loadSpriteData')
      await dispatch('initializeCallMonitoring')
    } catch (error) {
      console.error('初始化精灵数据失败:', error)
    }
  },

  // 初始化通话监听
  async initializeCallMonitoring({ dispatch }) {
    try {
      // 获取plugins对象
      const plugins = await dispatch('getPlugins')
      if (!plugins || !plugins.callbackPlugin) {
        console.warn('无法获取通话插件，跳过通话监听初始化')
        return
      }

      // 注册通话状态监听
      plugins.callbackPlugin.callStateNotified((data) => {
        console.log('通话状态变化:', data)
        dispatch('handleCallStateChange', data)
      })

      console.log('通话监听初始化完成')
    } catch (error) {
      console.error('通话监听初始化失败:', error)
    }
  },

  // 获取plugins对象
  async getPlugins() {
    // 从全局获取plugins对象
    if (window.plugins) {
      return window.plugins
    }

    // 从Vue实例获取
    if (this._vm && this._vm.$root && this._vm.$root.$children[0]) {
      const app = this._vm.$root.$children[0]
      if (app.plugins) {
        return app.plugins
      }
    }

    throw new Error('无法获取plugins对象')
  },

  // 处理通话状态变化
  async handleCallStateChange({ commit, state, getters, dispatch }, callData) {
    try {
      const callState = callData.callInfo && callData.callInfo.callState

      if (callState === 'CONNECTED') {
        // 通话开始
        await dispatch('startCallTimer')
      } else if (callState === 'DISCONNECTED' || callState === 'FAILED') {
        // 通话结束
        await dispatch('endCallTimer')
      }
    } catch (error) {
      console.error('处理通话状态变化失败:', error)
    }
  },
  
  // 认领精灵
  async claimSprite({ commit, state }, spriteTypeId) {
    const spriteType = state.spriteTypes.find(type => type.id === spriteTypeId)
    if (!spriteType) {
      throw new Error('精灵类型不存在')
    }
    
    const newSprite = {
      id: `sprite_${spriteTypeId}_${Date.now()}`,
      typeId: spriteTypeId,
      name: '通话精灵',
      level: 1,
      experience: 0,
      form: 1,
      hasGlowEffect: false,
      unlockTime: Date.now()
    }
    
    commit('ADD_SPRITE', newSprite)
    commit('SET_CURRENT_SPRITE', newSprite.id)
    commit('SET_USER_SPRITE_STATUS', true)
    commit('ADD_UNLOCKED_SPRITE', spriteTypeId)
    
    // 保存数据
    await this.dispatch('sprite/saveSpriteData')
    
    return newSprite
  },
  
  // 切换精灵
  async switchSprite({ commit, dispatch }, spriteId) {
    commit('SET_CURRENT_SPRITE', spriteId)
    await dispatch('saveSpriteData')
  },
  
  // 投喂精灵
  async feedSprite({ commit, state, getters, dispatch }) {
    const currentSprite = getters.currentSprite
    if (!currentSprite) {
      throw new Error('没有当前精灵')
    }
    
    const today = new Date().toDateString()
    const lastFeedDate = state.user.lastFeedDate
    
    // 检查是否今天已经投喂过
    if (lastFeedDate === today) {
      return { success: false, message: '今天已经投喂过了' }
    }
    
    // 增加经验值
    const newExperience = currentSprite.experience + 20
    commit('UPDATE_SPRITE_EXPERIENCE', { 
      spriteId: currentSprite.id, 
      experience: newExperience 
    })
    commit('SET_LAST_FEED_DATE', today)
    
    await dispatch('saveSpriteData')
    
    return { success: true, message: '投喂成功，获得20经验值！' }
  },
  
  // 开始通话计时
  startCallTimer({ commit, dispatch }) {
    const startTime = Date.now()
    commit('SET_CALL_STATE', {
      isInCall: true,
      startTime: startTime
    })

    // 开始实时更新通话时长
    dispatch('startCallDurationUpdate')

    console.log('开始通话计时:', new Date(startTime))
  },

  // 结束通话计时
  async endCallTimer({ commit, state, getters, dispatch }) {
    if (!state.callState.isInCall) return

    const endTime = Date.now()
    const callDuration = Math.floor((endTime - state.callState.callStartTime) / 60000) // 分钟

    console.log('结束通话计时，通话时长:', callDuration, '分钟')

    // 停止实时更新
    dispatch('stopCallDurationUpdate')

    if (callDuration > 0 && getters.currentSprite) {
      // 更新累计通话时长
      commit('UPDATE_TOTAL_CALL_TIME', callDuration)

      // 更新当前精灵经验值
      const currentSprite = getters.currentSprite
      const experienceGain = callDuration * 10
      const newExperience = currentSprite.experience + experienceGain

      console.log('通话获得经验值:', experienceGain)

      commit('UPDATE_SPRITE_EXPERIENCE', {
        spriteId: currentSprite.id,
        experience: newExperience
      })

      // 更新连续通话天数
      await dispatch('updateConsecutiveCallDays')

      // 检查新精灵解锁
      await dispatch('checkNewSpriteUnlock')

      // 保存数据
      await dispatch('saveSpriteData')

      // 触发经验值更新事件
      dispatch('notifyExperienceGain', experienceGain)
    }

    commit('SET_CALL_STATE', { isInCall: false })
  },

  // 开始实时更新通话时长
  startCallDurationUpdate({ commit, state }) {
    if (state.callDurationTimer) {
      clearInterval(state.callDurationTimer)
    }

    const timer = setInterval(() => {
      if (state.callState.isInCall && state.callState.callStartTime) {
        const currentDuration = Math.floor((Date.now() - state.callState.callStartTime) / 1000)
        commit('UPDATE_CURRENT_CALL_DURATION', currentDuration)
      }
    }, 1000)

    commit('SET_CALL_DURATION_TIMER', timer)
  },

  // 停止实时更新通话时长
  stopCallDurationUpdate({ commit, state }) {
    if (state.callDurationTimer) {
      clearInterval(state.callDurationTimer)
      commit('SET_CALL_DURATION_TIMER', null)
    }
  },

  // 通知经验值获得
  notifyExperienceGain(context, amount) {
    // 这里可以触发UI更新或其他副作用
    console.log('经验值获得通知:', amount)
    // 可以通过事件总线或其他方式通知组件
  },

  // 更新精灵名字
  async updateSpriteName({ commit, dispatch }, { spriteId, name }) {
    try {
      if (!spriteId || !name) {
        throw new Error('精灵ID和名字不能为空')
      }

      // 验证名字格式
      if (name.length > 10) {
        throw new Error('名字长度不能超过10个字符')
      }

      if (name.trim().length === 0) {
        throw new Error('名字不能为空')
      }

      // 更新精灵名字
      commit('UPDATE_SPRITE_NAME', { spriteId, name: name.trim() })

      // 保存数据
      await dispatch('saveSpriteData')

      console.log('精灵名字更新成功:', name)
      return true
    } catch (error) {
      console.error('更新精灵名字失败:', error)
      throw error
    }
  },
  
  // 更新连续通话天数
  updateConsecutiveCallDays({ commit, state }) {
    const today = new Date().toDateString()
    const lastCallDate = state.user.lastCallDate
    
    if (lastCallDate) {
      const lastDate = new Date(lastCallDate)
      const todayDate = new Date(today)
      const diffTime = todayDate.getTime() - lastDate.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        // 连续通话
        commit('UPDATE_CONSECUTIVE_CALL_DAYS', state.user.consecutiveCallDays + 1)
      } else if (diffDays > 1) {
        // 中断了，重新开始
        commit('UPDATE_CONSECUTIVE_CALL_DAYS', 1)
      }
    } else {
      // 第一次通话
      commit('UPDATE_CONSECUTIVE_CALL_DAYS', 1)
    }
    
    commit('SET_LAST_CALL_DATE', today)
  },
  
  // 检查新精灵解锁
  checkNewSpriteUnlock({ commit, state }) {
    state.spriteTypes.forEach(spriteType => {
      if (state.user.totalCallTime >= spriteType.unlockRequirement && 
          !state.user.unlockedSprites.includes(spriteType.id)) {
        commit('ADD_UNLOCKED_SPRITE', spriteType.id)
      }
    })
  },
  
  // 保存精灵数据
  async saveSpriteData({ state }) {
    try {
      const dataStorage = (await import('@/utils/dataStorage')).default

      // 确保数据存储已初始化
      if (!dataStorage.getInitializationStatus()) {
        console.warn('数据存储未初始化，跳过保存')
        return false
      }

      const saveData = {
        user: state.user,
        sprites: state.sprites,
        spriteTypes: state.spriteTypes,
        lastSaved: Date.now(),
        version: '1.0.0'
      }

      const success = await dataStorage.saveSpriteData(saveData)

      if (!success) {
        throw new Error('数据保存失败')
      }

      console.log('精灵数据保存成功')
      return true
    } catch (error) {
      console.error('保存精灵数据失败:', error)
      // 不抛出错误，避免影响正常流程
      return false
    }
  },

  // 加载精灵数据
  async loadSpriteData({ commit, state }) {
    try {
      const dataStorage = (await import('@/utils/dataStorage')).default

      // 确保数据存储已初始化
      if (!dataStorage.getInitializationStatus()) {
        console.warn('数据存储未初始化，使用默认数据')
        return
      }

      const data = await dataStorage.loadSpriteData()

      if (data && data.user) {
        // 数据版本兼容性检查
        if (data.version !== '1.0.0') {
          console.log('检测到数据版本差异，进行数据迁移')
          // 这里可以添加数据迁移逻辑
        }

        commit('LOAD_STATE', {
          user: { ...state.user, ...data.user },
          sprites: data.sprites || {},
          spriteTypes: data.spriteTypes || state.spriteTypes,
          callState: state.callState, // 保持当前通话状态
          callDurationTimer: state.callDurationTimer // 保持定时器状态
        })

        console.log('精灵数据加载成功')
      } else {
        console.log('没有找到保存的精灵数据，使用默认数据')
      }
    } catch (error) {
      console.error('加载精灵数据失败:', error)
      // 不抛出错误，使用默认数据
    }
  },

  // 备份精灵数据
  async backupSpriteData({ state }) {
    try {
      const dataStorage = (await import('@/utils/dataStorage')).default

      const backupData = {
        ...state,
        backupTime: Date.now(),
        backupVersion: '1.0.0'
      }

      const success = await dataStorage.save('sprite_backup', backupData)

      if (success) {
        console.log('精灵数据备份成功')
      }

      return success
    } catch (error) {
      console.error('备份精灵数据失败:', error)
      return false
    }
  },

  // 恢复精灵数据
  async restoreSpriteData({ commit }) {
    try {
      const dataStorage = (await import('@/utils/dataStorage')).default

      const backupData = await dataStorage.load('sprite_backup')

      if (backupData && backupData.user) {
        commit('LOAD_STATE', {
          user: backupData.user,
          sprites: backupData.sprites || {},
          spriteTypes: backupData.spriteTypes || state.spriteTypes
        })

        console.log('精灵数据恢复成功')
        return true
      } else {
        console.log('没有找到备份数据')
        return false
      }
    } catch (error) {
      console.error('恢复精灵数据失败:', error)
      return false
    }
  },

  // 清除所有精灵数据
  async clearAllSpriteData({ commit }) {
    try {
      const dataStorage = (await import('@/utils/dataStorage')).default

      // 清除主数据
      await dataStorage.remove('sprite_data')

      // 清除备份数据
      await dataStorage.remove('sprite_backup')

      // 重置状态到初始值
      commit('LOAD_STATE', {
        user: {
          hasSprite: false,
          currentSpriteId: null,
          unlockedSprites: [],
          totalCallTime: 0,
          consecutiveCallDays: 0,
          lastCallDate: null,
          lastFeedDate: null,
        },
        sprites: {},
        spriteTypes: state.spriteTypes
      })

      console.log('所有精灵数据已清除')
      return true
    } catch (error) {
      console.error('清除精灵数据失败:', error)
      return false
    }
  },

  // 处理对方同步事件
  handlePeerSyncEvent({ commit }, { eventType, data }) {
    console.log('处理对方同步事件:', eventType, data)

    // 这里可以根据事件类型触发相应的UI更新
    // 例如显示对方精灵的状态变化
    switch (eventType) {
      case 'peer_experience_update':
        // 可以在UI中显示对方获得经验值的提示
        break
      case 'peer_name_update':
        // 可以在UI中显示对方修改精灵名字的提示
        break
      case 'peer_sprite_switch':
        // 可以在UI中显示对方切换精灵的提示
        break
      case 'peer_call_time_update':
        // 可以在UI中显示对方通话时长的变化
        break
      case 'peer_feed_update':
        // 可以在UI中显示对方投喂精灵的动画
        break
      default:
        console.log('未处理的对方同步事件:', eventType)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}

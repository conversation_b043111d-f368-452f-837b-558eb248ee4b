webpackJsonp([4],{QUIi:function(t,i){},b4kT:function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var s=e("s/F+"),a=e.n(s),r=e("yrbl"),n=e.n(r),c=e("vvmr"),o=e.n(c),l=e("nVSy"),v={name:"SpriteClaimPage",data:function(){return{isLoading:!1,isProcessing:!1,showSparkles:!1,previewSprite:{image:e("oLbb"),name:"通话精灵"}}},computed:o()({},Object(l.d)("sprite",["spriteTypes"])),mounted:function(){this.initPage(),this.startSparkleAnimation()},methods:o()({},Object(l.b)("sprite",["claimSprite","initializeSpriteData"]),{initPage:function(){var t=this;return n()(a.a.mark(function i(){return a.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return t.isLoading=!0,i.prev=1,i.next=4,t.initializeSpriteData();case 4:console.log("精灵数据初始化完成"),i.next=11;break;case 7:i.prev=7,i.t0=i.catch(1),console.error("初始化失败:",i.t0),t.$message.error("初始化失败，请重试");case 11:return i.prev=11,t.isLoading=!1,i.finish(11);case 14:case"end":return i.stop()}},i,t,[[1,7,11,14]])}))()},handleClaimSprite:function(){var t=this;return n()(a.a.mark(function i(){var e;return a.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(!t.isProcessing){i.next=2;break}return i.abrupt("return");case 2:return t.isProcessing=!0,i.prev=3,i.next=6,t.claimSprite(1);case 6:e=i.sent,t.$message.success("恭喜！精灵认领成功！"),t.playSuccessAnimation(),setTimeout(function(){t.$router.push("/sprite-interaction")},2e3),console.log("精灵认领成功:",e),i.next=17;break;case 13:i.prev=13,i.t0=i.catch(3),console.error("精灵认领失败:",i.t0),t.$message.error("认领失败，请重试");case 17:return i.prev=17,t.isProcessing=!1,i.finish(17);case 20:case"end":return i.stop()}},i,t,[[3,13,17,20]])}))()},playSuccessAnimation:function(){var t=this,i=this.$el.querySelector(".sprite-avatar");i&&(i.classList.add("success-animation"),setTimeout(function(){i.classList.remove("success-animation")},600)),this.showSparkles=!0,setTimeout(function(){t.showSparkles=!1},3e3)},startSparkleAnimation:function(){var t=this;setInterval(function(){t.showSparkles=!0,setTimeout(function(){t.showSparkles=!1},2e3)},5e3)},handleImageError:function(){this.previewSprite.image="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjYwIiBjeT0iNjAiIHI9IjMwIiBmaWxsPSIjRTBFMEUwIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjMiIGZpbGw9IiMzMzMiLz4KPGNpcmNsZSBjeD0iNzAiIGN5PSI1MCIgcj0iMyIgZmlsbD0iIzMzMyIvPgo8cGF0aCBkPSJNNDUgNzBRNjAgODAgNzUgNzAiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo="}})},p={render:function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticClass:"sprite-container"},[t._m(0),t._v(" "),e("div",{staticClass:"sprite-card"},[e("div",{staticClass:"claim-content"},[e("div",{staticClass:"sprite-preview"},[e("img",{staticClass:"sprite-avatar",attrs:{src:t.previewSprite.image,alt:"精灵预览"},on:{error:t.handleImageError}}),t._v(" "),t.showSparkles?e("div",{staticClass:"sparkle-effects"},t._l(6,function(t){return e("div",{key:t,staticClass:"sparkle"})}),0):t._e()]),t._v(" "),e("h2",{staticClass:"claim-title"},[t._v("认领你的第一只精灵")]),t._v(" "),t._m(1),t._v(" "),e("el-button",{staticClass:"claim-btn",attrs:{type:"primary",size:"large",loading:t.isProcessing},on:{click:t.handleClaimSprite}},[t._v("\n        "+t._s(t.isProcessing?"正在认领...":"领养一个精灵")+"\n      ")]),t._v(" "),t._m(2)],1)]),t._v(" "),t.isLoading?e("div",{staticClass:"loading-overlay"},[e("div",{staticClass:"loading-spinner"}),t._v(" "),e("p",[t._v("正在初始化精灵世界...")])]):t._e()])},staticRenderFns:[function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"sprite-header"},[i("h1",{staticClass:"title"},[this._v("欢迎来到通话精灵世界")]),this._v(" "),i("p",{staticClass:"subtitle"},[this._v("开始你的精灵冒险之旅")])])},function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticClass:"claim-description"},[e("p",[t._v("通话精灵将陪伴你的每一次通话，")]),t._v(" "),e("p",[t._v("通过通话时长获得经验值，让精灵不断成长！")]),t._v(" "),e("br"),t._v(" "),e("div",{staticClass:"feature-list"},[e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-phone"}),t._v(" "),e("span",[t._v("通话时长转化为经验值")])]),t._v(" "),e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-food"}),t._v(" "),e("span",[t._v("每日投喂获得额外奖励")])]),t._v(" "),e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-star-on"}),t._v(" "),e("span",[t._v("连续通话获得闪光特效")])]),t._v(" "),e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-trophy"}),t._v(" "),e("span",[t._v("解锁更多精灵形象")])])])])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"tips"},[i("p",{staticClass:"tip-text"},[i("i",{staticClass:"el-icon-info"}),this._v("\n          认领后即可开始你的精灵养成之旅\n        ")])])}]};var u=e("l2X3")(v,p,!1,function(t){e("QUIi")},"data-v-52a07956",null);i.default=u.exports}});
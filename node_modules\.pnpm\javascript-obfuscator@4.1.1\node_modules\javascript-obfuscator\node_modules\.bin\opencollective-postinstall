#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/opencollective-postinstall@2.0.3/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/index.js" "$@"
else
  exec node  "$basedir/../../../../../opencollective-postinstall@2.0.3/node_modules/opencollective-postinstall/index.js" "$@"
fi

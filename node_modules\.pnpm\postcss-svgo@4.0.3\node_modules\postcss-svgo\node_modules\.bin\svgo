#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/svgo@1.3.2/node_modules/svgo/bin/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/svgo@1.3.2/node_modules/svgo/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/svgo@1.3.2/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/svgo@1.3.2/node_modules/svgo/bin/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/svgo@1.3.2/node_modules/svgo/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/svgo@1.3.2/node_modules:/mnt/c/workspaces/zjh/dc/sprite/miniapp-jssdk-demo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../svgo@1.3.2/node_modules/svgo/bin/svgo" "$@"
else
  exec node  "$basedir/../../../../../svgo@1.3.2/node_modules/svgo/bin/svgo" "$@"
fi

webpackJsonp([1],{"/dhm":function(t,e){},"5Qs7":function(t,e){},DdYH:function(t,e,i){var s={"./sprite1_form1.png":"oLbb"};function a(t){return i(n(t))}function n(t){var e=s[t];if(!(e+1))throw new Error("Cannot find module '"+t+"'.");return e}a.keys=function(){return Object.keys(s)},a.resolve=n,t.exports=a,a.id="DdYH"},Wd6G:function(t,e){},XGaV:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i("s/F+"),a=i.n(s),n=i("yrbl"),r=i.n(n),c=i("vvmr"),l=i.n(c),o=i("nVSy"),u={name:"ExperienceBar",props:{current:{type:Number,default:0},total:{type:Number,default:50},percentage:{type:Number,default:0},needed:{type:Number,default:50},showGlow:{type:Boolean,default:!1},animationDuration:{type:Number,default:1e3}},data:function(){return{displayPercentage:0,isAnimating:!1,showExpGain:!1,showLevelUpEffect:!1,showLevelUpMessage:!1,expGainAmount:0,previousPercentage:0,animationTimer:null}},watch:{percentage:{handler:function(t,e){this.animateProgress(e,t)},immediate:!0}},mounted:function(){this.displayPercentage=this.percentage,this.previousPercentage=this.percentage},beforeDestroy:function(){this.animationTimer&&clearTimeout(this.animationTimer)},methods:{animateProgress:function(t,e){if(t!==e){this.isAnimating=!0,this.previousPercentage=t||0;var i=this.calculateExpGain(t,e);i>0&&this.showExpGainEffect(i);var s=this.checkLevelUp(t,e);this.animateProgressBar(this.previousPercentage,e,s)}},animateProgressBar:function(t,e){var i=this,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=this.animationDuration,n=Date.now();requestAnimationFrame(function r(){var c=Date.now()-n,l=Math.min(c/a,1),o=i.easeOutCubic(l);i.displayPercentage=t+(e-t)*o,l<1?requestAnimationFrame(r):(i.displayPercentage=e,i.isAnimating=!1,s&&i.showLevelUpEffects())})},easeOutCubic:function(t){return 1-Math.pow(1-t,3)},calculateExpGain:function(t,e){var i=t/100*this.total,s=e/100*this.total;return Math.round(s-i)},checkLevelUp:function(t,e){return t>e&&t>80},showExpGainEffect:function(t){var e=this;this.expGainAmount=t,this.showExpGain=!0,setTimeout(function(){e.showExpGain=!1},2e3)},showLevelUpEffects:function(){var t=this;this.showLevelUpEffect=!0,setTimeout(function(){t.showLevelUpEffect=!1},2e3),setTimeout(function(){t.showLevelUpMessage=!0,setTimeout(function(){t.showLevelUpMessage=!1},3e3)},500);var e=Math.floor((this.percentage/100*this.total+this.current)/this.total)+1;this.$emit("level-up",e)},handleProgressClick:function(t){var e=t.currentTarget.getBoundingClientRect(),i=(t.clientX-e.left)/e.width*100;this.createRippleEffect(t.clientX-e.left,t.clientY-e.top),this.$emit("progress-click",i)},createRippleEffect:function(t,e){var i=document.createElement("div");i.className="click-ripple",i.style.left=t+"px",i.style.top=e+"px";var s=this.$el.querySelector(".progress-bar");s.appendChild(i),setTimeout(function(){s.contains(i)&&s.removeChild(i)},600)},triggerExpGain:function(t){this.showExpGainEffect(t)},triggerLevelUp:function(){this.showLevelUpEffects()}}},d={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"experience-bar"},[i("div",{staticClass:"exp-info"},[i("div",{staticClass:"exp-current"},[i("span",{staticClass:"exp-label"},[t._v("经验值:")]),t._v(" "),i("span",{staticClass:"exp-value"},[t._v(t._s(t.current)+"/"+t._s(t.total))])]),t._v(" "),i("div",{staticClass:"exp-needed"},[i("span",{staticClass:"needed-label"},[t._v("距离下一级:")]),t._v(" "),i("span",{staticClass:"needed-value"},[t._v(t._s(t.needed)+"EXP")])])]),t._v(" "),i("div",{staticClass:"progress-container",on:{click:t.handleProgressClick}},[i("div",{staticClass:"progress-bar"},[i("div",{staticClass:"progress-background"}),t._v(" "),i("div",{staticClass:"progress-fill",class:{"exp-growth":t.isAnimating},style:{width:t.displayPercentage+"%"}}),t._v(" "),t.showGlow?i("div",{staticClass:"progress-glow",style:{width:t.displayPercentage+"%"}}):t._e(),t._v(" "),t.showLevelUpEffect?i("div",{staticClass:"level-up-effect"},t._l(6,function(t){return i("div",{key:t,staticClass:"sparkle"})}),0):t._e()]),t._v(" "),i("div",{staticClass:"percentage-display"},[t._v("\n      "+t._s(Math.round(t.displayPercentage))+"%\n    ")])]),t._v(" "),i("transition",{attrs:{name:"exp-gain"}},[t.showExpGain?i("div",{staticClass:"exp-gain-indicator"},[t._v("\n      +"+t._s(t.expGainAmount)+"EXP\n    ")]):t._e()]),t._v(" "),i("transition",{attrs:{name:"level-up"}},[t.showLevelUpMessage?i("div",{staticClass:"level-up-message"},[i("i",{staticClass:"el-icon-trophy"}),t._v("\n      恭喜升级！\n    ")]):t._e()])],1)},staticRenderFns:[]};var v=i("l2X3")(u,d,!1,function(t){i("Wd6G")},"data-v-0e166184",null).exports,p=i("Ov2S"),f=i.n(p),h={name:"SpriteNameEditor",props:{name:{type:String,default:"通话精灵"},editable:{type:Boolean,default:!0},maxLength:{type:Number,default:10},placeholder:{type:String,default:"请输入精灵名字"},rules:{type:Array,default:function(){return[]}}},data:function(){return{isEditing:!1,editingName:"",originalName:"",isSaving:!1,errorMessage:"",hasError:!1}},computed:{displayName:function(){return this.name||"通话精灵"}},watch:{name:{handler:function(t){this.isEditing||(this.editingName=t||"")},immediate:!0},editingName:function(){this.hasError&&this.clearError()}},methods:{startEdit:function(){var t=this;this.editable&&(this.isEditing=!0,this.editingName=this.name||"",this.originalName=this.name||"",this.clearError(),this.$nextTick(function(){t.$refs.nameInput&&(t.$refs.nameInput.focus(),t.$refs.nameInput.select())}),this.$emit("edit-start"))},confirmEdit:function(){var t=this;return r()(a.a.mark(function e(){var i,s;return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isSaving){e.next=2;break}return e.abrupt("return");case 2:if(i=t.editingName.trim(),(s=t.validateName(i)).valid){e.next=7;break}return t.showError(s.message),e.abrupt("return");case 7:if(i!==t.originalName){e.next=10;break}return t.cancelEdit(),e.abrupt("return");case 10:return t.isSaving=!0,e.prev=11,e.next=14,t.$emit("name-change",i);case 14:t.isEditing=!1,t.clearError(),t.$message.success("名字修改成功！"),t.$emit("edit-success",i),e.next=25;break;case 20:e.prev=20,e.t0=e.catch(11),console.error("名字保存失败:",e.t0),t.showError("保存失败，请重试"),t.$emit("edit-error",e.t0);case 25:return e.prev=25,t.isSaving=!1,e.finish(25);case 28:case"end":return e.stop()}},e,t,[[11,20,25,28]])}))()},cancelEdit:function(){this.isEditing=!1,this.editingName=this.originalName,this.clearError(),this.$emit("edit-cancel")},handleBlur:function(){var t=this;setTimeout(function(){t.isEditing&&!t.isSaving&&t.confirmEdit()},200)},validateName:function(t){if(!t)return{valid:!1,message:"名字不能为空"};if(t.length>this.maxLength)return{valid:!1,message:"名字长度不能超过"+this.maxLength+"个字符"};if(/[<>'"&]/.test(t))return{valid:!1,message:"名字包含无效字符"};if(0===t.trim().length)return{valid:!1,message:"名字不能只包含空格"};var e=!0,i=!1,s=void 0;try{for(var a,n=f()(this.rules);!(e=(a=n.next()).done);e=!0){var r=a.value;if("function"==typeof r){var c=r(t);if(!0!==c)return{valid:!1,message:c||"名字格式不正确"}}}}catch(t){i=!0,s=t}finally{try{!e&&n.return&&n.return()}finally{if(i)throw s}}return{valid:!0}},showError:function(t){var e=this;this.hasError=!0,this.errorMessage=t,this.$refs.nameInput&&(this.$refs.nameInput.$el.classList.add("error-shake"),setTimeout(function(){e.$refs.nameInput.$el.classList.remove("error-shake")},500))},clearError:function(){this.hasError=!1,this.errorMessage=""},triggerEdit:function(){this.startEdit()},setName:function(t){this.isEditing||(this.editingName=t||"")}}},m={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"sprite-name-editor"},[t.isEditing?i("div",{staticClass:"name-edit"},[i("el-input",{ref:"nameInput",staticClass:"name-input",class:{error:t.hasError},attrs:{maxlength:t.maxLength,placeholder:t.placeholder,size:"small"},on:{blur:t.handleBlur,keyup:[function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.confirmEdit.apply(null,arguments)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"esc",27,e.key,["Esc","Escape"])?null:t.cancelEdit.apply(null,arguments)}]},model:{value:t.editingName,callback:function(e){t.editingName=e},expression:"editingName"}},[i("template",{slot:"suffix"},[i("span",{staticClass:"char-count"},[t._v(t._s(t.editingName.length)+"/"+t._s(t.maxLength))])])],2),t._v(" "),i("div",{staticClass:"edit-actions"},[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-check",loading:t.isSaving},on:{click:t.confirmEdit}},[t._v("\n        确定\n      ")]),t._v(" "),i("el-button",{attrs:{size:"mini",icon:"el-icon-close"},on:{click:t.cancelEdit}},[t._v("\n        取消\n      ")])],1),t._v(" "),t.errorMessage?i("div",{staticClass:"error-message"},[i("i",{staticClass:"el-icon-warning"}),t._v("\n      "+t._s(t.errorMessage)+"\n    ")]):t._e(),t._v(" "),t._m(0)],1):i("div",{staticClass:"name-display",on:{click:t.startEdit}},[i("span",{staticClass:"sprite-name"},[t._v(t._s(t.displayName))]),t._v(" "),t.editable?i("i",{staticClass:"el-icon-edit edit-icon",attrs:{title:"点击编辑名字"}}):t._e()])])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"edit-tips"},[e("p",{staticClass:"tip-text"},[e("i",{staticClass:"el-icon-info"}),this._v("\n        按Enter确认，按Esc取消\n      ")])])}]};var g=i("l2X3")(h,m,!1,function(t){i("5Qs7")},"data-v-3f64bab6",null).exports,_=i("SaMe"),C=i.n(_),b={name:"InteractionButtons",props:{sprite:{type:Object,default:null},canFeed:{type:Boolean,default:!0},isInteracting:{type:Boolean,default:!1}},data:function(){return{feedEffect:!1,touchEffect:!1,bathEffect:!1,showInteractionTip:!1,interactionTipText:"",cooldownTimers:{feed:null,touch:null,bath:null}}},methods:{handleFeed:function(){this.isInteracting||(this.playButtonEffect("feed"),this.canFeed?this.showTip("精灵很开心，获得了20经验值！"):this.showTip("今天已经投喂过了，明天再来吧~"),this.$emit("feed"),this.setCooldown("feed",2e3))},handleTouch:function(){this.isInteracting||(this.playButtonEffect("touch"),this.showTip("精灵感受到了你的关爱！"),this.$emit("touch"),this.setCooldown("touch",1500))},handleBath:function(){this.isInteracting||(this.playButtonEffect("bath"),this.showTip("精灵变得更加干净了！"),this.$emit("bath"),this.setCooldown("bath",2e3))},playButtonEffect:function(t){var e=this;switch(t){case"feed":this.feedEffect=!0,setTimeout(function(){e.feedEffect=!1},1e3);break;case"touch":this.touchEffect=!0,setTimeout(function(){e.touchEffect=!1},1e3);break;case"bath":this.bathEffect=!0,setTimeout(function(){e.bathEffect=!1},1e3)}var i=this.$el.querySelector("."+t+"-btn");i&&(i.classList.add("button-press"),setTimeout(function(){i.classList.remove("button-press")},200))},showTip:function(t){var e=this;this.interactionTipText=t,this.showInteractionTip=!0,setTimeout(function(){e.showInteractionTip=!1},3e3)},setCooldown:function(t,e){var i=this;this.cooldownTimers[t]&&clearTimeout(this.cooldownTimers[t]);var s=this.$el.querySelector("."+t+"-btn");s&&(s.classList.add("cooldown"),this.cooldownTimers[t]=setTimeout(function(){s.classList.remove("cooldown"),i.cooldownTimers[t]=null},e))},getFeedStatusText:function(){return this.canFeed?"可获得经验值":"今日已投喂"}},beforeDestroy:function(){C()(this.cooldownTimers).forEach(function(t){t&&clearTimeout(t)})}},E={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"interaction-buttons"},[i("el-button",{staticClass:"interaction-btn feed-btn",class:{"can-feed":t.canFeed,"fed-today":!t.canFeed},attrs:{disabled:t.isInteracting},on:{click:t.handleFeed}},[i("div",{staticClass:"btn-content"},[i("i",{staticClass:"btn-icon"},[t._v("🍩")]),t._v(" "),i("span",{staticClass:"btn-text"},[t._v("投喂")]),t._v(" "),t.canFeed?i("div",{staticClass:"exp-badge"},[t._v("+20EXP")]):i("div",{staticClass:"fed-indicator"},[t._v("今日已投喂")])]),t._v(" "),t.feedEffect?i("div",{staticClass:"button-effect feed-effect"},t._l(3,function(t){return i("div",{key:t,staticClass:"donut-particle"})}),0):t._e()]),t._v(" "),i("el-button",{staticClass:"interaction-btn touch-btn",attrs:{disabled:t.isInteracting},on:{click:t.handleTouch}},[i("div",{staticClass:"btn-content"},[i("i",{staticClass:"btn-icon"},[t._v("❤️")]),t._v(" "),i("span",{staticClass:"btn-text"},[t._v("抚摸")]),t._v(" "),i("div",{staticClass:"coming-soon"},[t._v("敬请期待")])]),t._v(" "),t.touchEffect?i("div",{staticClass:"button-effect touch-effect"},t._l(5,function(t){return i("div",{key:t,staticClass:"heart-particle"})}),0):t._e()]),t._v(" "),i("el-button",{staticClass:"interaction-btn bath-btn",attrs:{disabled:t.isInteracting},on:{click:t.handleBath}},[i("div",{staticClass:"btn-content"},[i("i",{staticClass:"btn-icon"},[t._v("🛁")]),t._v(" "),i("span",{staticClass:"btn-text"},[t._v("洗澡")]),t._v(" "),i("div",{staticClass:"coming-soon"},[t._v("敬请期待")])]),t._v(" "),t.bathEffect?i("div",{staticClass:"button-effect bath-effect"},t._l(4,function(t){return i("div",{key:t,staticClass:"water-particle"})}),0):t._e()]),t._v(" "),t.showInteractionTip?i("div",{staticClass:"interaction-tip"},[i("transition",{attrs:{name:"tip-fade"}},[i("div",{staticClass:"tip-content"},[i("i",{staticClass:"el-icon-info"}),t._v("\n        "+t._s(t.interactionTipText)+"\n      ")])])],1):t._e()],1)},staticRenderFns:[]};var y=i("l2X3")(b,E,!1,function(t){i("/dhm")},"data-v-1d4b06d3",null).exports,w={name:"SpriteUpgradeRules",props:{visible:{type:Boolean,default:!1}},data:function(){return{dialogVisible:!1,calcMinutes:10,calcFeeds:1,calculatedExp:120,calculatedLevel:3,spriteFormRules:[{id:1,name:"幼体形态",levelRange:"1-5级",description:"刚刚诞生的精灵，充满好奇心",image:i("uVB+")},{id:2,name:"成长形态",levelRange:"6-15级",description:"正在成长的精灵，活力四射",image:i("uVB+")},{id:3,name:"完全体形态",levelRange:"16-99级",description:"完全成熟的精灵，威风凛凛",image:i("uVB+")}]}},watch:{visible:{handler:function(t){this.dialogVisible=t},immediate:!0},dialogVisible:function(t){t||this.$emit("close")}},mounted:function(){this.calculateExp()},methods:{handleClose:function(){this.dialogVisible=!1},calculateExp:function(){var t=10*this.calcMinutes,e=20*this.calcFeeds;this.calculatedExp=t+e,this.calculatedLevel=Math.floor(this.calculatedExp/50)+1},handleImageError:function(t){t.target.src=i("uVB+")}}},x={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-dialog",{staticClass:"upgrade-rules-dialog",attrs:{title:"等级提升规则",visible:t.dialogVisible,width:"90%","before-close":t.handleClose,center:""},on:{"update:visible":function(e){t.dialogVisible=e}}},[i("div",{staticClass:"rules-content"},[i("div",{staticClass:"rules-section"},[i("h3",{staticClass:"section-title"},[i("i",{staticClass:"el-icon-trophy"}),t._v("\n        升级规则概述\n      ")]),t._v(" "),i("div",{staticClass:"rule-item"},[i("div",{staticClass:"rule-icon"},[t._v("📈")]),t._v(" "),i("div",{staticClass:"rule-text"},[i("strong",[t._v("经验值获取：")]),t._v("通话时长每分钟获得10经验值\n        ")])]),t._v(" "),i("div",{staticClass:"rule-item"},[i("div",{staticClass:"rule-icon"},[t._v("🍩")]),t._v(" "),i("div",{staticClass:"rule-text"},[i("strong",[t._v("投喂奖励：")]),t._v("每天首次投喂获得20经验值奖励\n        ")])]),t._v(" "),i("div",{staticClass:"rule-item"},[i("div",{staticClass:"rule-icon"},[t._v("⭐")]),t._v(" "),i("div",{staticClass:"rule-text"},[i("strong",[t._v("升级条件：")]),t._v("每级需要50经验值\n        ")])])]),t._v(" "),i("div",{staticClass:"rules-section"},[i("h3",{staticClass:"section-title"},[i("i",{staticClass:"el-icon-magic-stick"}),t._v("\n        精灵形态变化\n      ")]),t._v(" "),i("div",{staticClass:"forms-grid"},t._l(t.spriteFormRules,function(e){return i("div",{key:e.id,staticClass:"form-card"},[i("div",{staticClass:"form-header"},[i("img",{staticClass:"form-image",attrs:{src:e.image,alt:e.name},on:{error:t.handleImageError}}),t._v(" "),i("h4",{staticClass:"form-name"},[t._v(t._s(e.name))])]),t._v(" "),i("div",{staticClass:"form-details"},[i("div",{staticClass:"level-range"},[t._v(t._s(e.levelRange))]),t._v(" "),i("div",{staticClass:"form-description"},[t._v(t._s(e.description))])])])}),0)]),t._v(" "),i("div",{staticClass:"rules-section"},[i("h3",{staticClass:"section-title"},[i("i",{staticClass:"el-icon-star-on"}),t._v("\n        特殊奖励\n      ")]),t._v(" "),i("div",{staticClass:"reward-item"},[i("div",{staticClass:"reward-icon"},[t._v("✨")]),t._v(" "),i("div",{staticClass:"reward-content"},[i("div",{staticClass:"reward-title"},[t._v("闪光特效")]),t._v(" "),i("div",{staticClass:"reward-description"},[t._v("连续通话3天以上获得闪光特效")])])]),t._v(" "),i("div",{staticClass:"reward-item"},[i("div",{staticClass:"reward-icon"},[t._v("🎁")]),t._v(" "),i("div",{staticClass:"reward-content"},[i("div",{staticClass:"reward-title"},[t._v("新精灵解锁")]),t._v(" "),i("div",{staticClass:"reward-description"},[t._v("累计通话1小时解锁新精灵形象")])])])]),t._v(" "),i("div",{staticClass:"rules-section"},[i("h3",{staticClass:"section-title"},[i("i",{staticClass:"el-icon-calculator"}),t._v("\n        经验值计算器\n      ")]),t._v(" "),i("div",{staticClass:"calculator"},[i("div",{staticClass:"calc-input"},[i("label",[t._v("通话时长（分钟）：")]),t._v(" "),i("el-input-number",{attrs:{min:0,max:999,size:"small"},on:{change:t.calculateExp},model:{value:t.calcMinutes,callback:function(e){t.calcMinutes=e},expression:"calcMinutes"}})],1),t._v(" "),i("div",{staticClass:"calc-input"},[i("label",[t._v("投喂次数：")]),t._v(" "),i("el-input-number",{attrs:{min:0,max:30,size:"small"},on:{change:t.calculateExp},model:{value:t.calcFeeds,callback:function(e){t.calcFeeds=e},expression:"calcFeeds"}})],1),t._v(" "),i("div",{staticClass:"calc-result"},[i("div",{staticClass:"result-item"},[i("span",{staticClass:"result-label"},[t._v("总经验值：")]),t._v(" "),i("span",{staticClass:"result-value"},[t._v(t._s(t.calculatedExp)+"EXP")])]),t._v(" "),i("div",{staticClass:"result-item"},[i("span",{staticClass:"result-label"},[t._v("可达等级：")]),t._v(" "),i("span",{staticClass:"result-value"},[t._v("Lv."+t._s(t.calculatedLevel))])])])])]),t._v(" "),i("div",{staticClass:"rules-section"},[i("h3",{staticClass:"section-title"},[i("i",{staticClass:"el-icon-info"}),t._v("\n        升级小贴士\n      ")]),t._v(" "),i("div",{staticClass:"tips-list"},[i("div",{staticClass:"tip-item"},[i("i",{staticClass:"el-icon-check"}),t._v(" "),i("span",[t._v("保持每日通话，连续通话可获得闪光特效")])]),t._v(" "),i("div",{staticClass:"tip-item"},[i("i",{staticClass:"el-icon-check"}),t._v(" "),i("span",[t._v("记得每天投喂精灵，获得额外经验值奖励")])]),t._v(" "),i("div",{staticClass:"tip-item"},[i("i",{staticClass:"el-icon-check"}),t._v(" "),i("span",[t._v("通话时长越长，获得的经验值越多")])]),t._v(" "),i("div",{staticClass:"tip-item"},[i("i",{staticClass:"el-icon-check"}),t._v(" "),i("span",[t._v("达到一定通话时长可解锁新的精灵形象")])])])])]),t._v(" "),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:t.handleClose}},[t._v("关闭")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.handleClose}},[t._v("我知道了")])],1)])},staticRenderFns:[]};var I=i("l2X3")(w,x,!1,function(t){i("dPYC")},"data-v-3f9184b9",null).exports,S=i("gFrU"),T=i("3xks"),k={name:"SpriteInteractionPage",components:{ExperienceBar:v,SpriteNameEditor:g,InteractionButtons:y,SpriteUpgradeRules:I},data:function(){return{isLoading:!1,isInteracting:!1,showUpgradeRules:!1,callDurationTimer:null}},computed:l()({},Object(o.d)("sprite",["user","sprites","spriteTypes","callState"]),Object(o.c)("sprite",["currentSprite","currentSpriteLevel","currentSpriteForm","experienceProgress","hasGlowEffect"]),{currentSpriteName:function(){return this.currentSprite?this.currentSprite.name:"通话精灵"},currentLevel:function(){return this.currentSpriteLevel},formDescription:function(){return Object(S.c)(this.currentSpriteForm)},currentSpriteImage:function(){if(!this.currentSprite)return i("uVB+");try{return i("DdYH")("./sprite"+this.currentSprite.typeId+"_form"+this.currentSpriteForm+".png")}catch(t){return i("uVB+")}},canFeedToday:function(){return Object(S.a)(this.user.lastFeedDate).willGetExp},formatCallDuration:function(){if(!this.callState.isInCall)return"00:00";var t=this.callState.currentCallDuration,e=t%60;return Math.floor(t/60).toString().padStart(2,"0")+":"+e.toString().padStart(2,"0")}}),mounted:function(){var t=this;return r()(a.a.mark(function e(){return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.initializePage();case 2:t.startCallDurationTimer(),t.checkGlowEffect();case 4:case"end":return e.stop()}},e,t)}))()},beforeDestroy:function(){this.stopCallDurationTimer()},methods:l()({},Object(o.b)("sprite",["initializeSpriteData","feedSprite","switchSprite"]),{initializePage:function(){var t=this;return r()(a.a.mark(function e(){return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.prev=1,e.next=4,t.initializeSpriteData();case 4:if(t.currentSprite){e.next=8;break}return t.$message.warning("请先认领一只精灵"),t.$router.push("/sprite-claim"),e.abrupt("return");case 8:console.log("精灵互动页面初始化完成"),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(1),console.error("页面初始化失败:",e.t0),t.$message.error("初始化失败，请重试");case 15:return e.prev=15,t.isLoading=!1,e.finish(15);case 18:case"end":return e.stop()}},e,t,[[1,11,15,18]])}))()},handleNameChange:function(t){var e=this;return r()(a.a.mark(function i(){return a.a.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:if(i.prev=0,e.currentSprite){i.next=4;break}return e.$message.error("没有当前精灵"),i.abrupt("return");case 4:return i.next=6,e.$store.dispatch("sprite/updateSpriteName",{spriteId:e.currentSprite.id,name:t});case 6:e.$message.success("名字修改成功！"),i.next=13;break;case 9:i.prev=9,i.t0=i.catch(0),console.error("名字修改失败:",i.t0),e.$message.error(i.t0.message||"名字修改失败");case 13:case"end":return i.stop()}},i,e,[[0,9]])}))()},handleFeed:function(){var t=this;return r()(a.a.mark(function e(){var i;return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isInteracting){e.next=2;break}return e.abrupt("return");case 2:return t.isInteracting=!0,e.prev=3,e.next=6,t.feedSprite();case 6:(i=e.sent).success?(t.$message.success(i.message),t.playFeedAnimation()):(t.$message.info(i.message),t.playFeedAnimation(!1)),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(3),console.error("投喂失败:",e.t0),t.$message.error("投喂失败，请重试");case 14:return e.prev=14,setTimeout(function(){t.isInteracting=!1},1e3),e.finish(14);case 17:case"end":return e.stop()}},e,t,[[3,10,14,17]])}))()},handleTouch:function(){var t=this;this.isInteracting||(this.isInteracting=!0,this.playTouchAnimation(),setTimeout(function(){t.isInteracting=!1},1e3))},handleBath:function(){var t=this;this.isInteracting||(this.isInteracting=!0,this.playBathAnimation(),setTimeout(function(){t.isInteracting=!1},1e3))},playFeedAnimation:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=this.$el.querySelector(".sprite-avatar-container");e&&T.a.playFeedAnimation(e,t)},playTouchAnimation:function(){var t=this.$el.querySelector(".sprite-avatar-container");t&&T.a.playTouchAnimation(t)},playBathAnimation:function(){var t=this.$el.querySelector(".sprite-avatar-container");t&&T.a.playBathAnimation(t)},playLevelUpAnimation:function(t){var e=this.$el.querySelector(".sprite-avatar-container");e&&T.a.playLevelUpAnimation(e,t)},playGlowEffect:function(){var t=this.$el.querySelector(".sprite-avatar-container");t&&T.a.playGlowEffect(t)},handleLevelUp:function(t){var e=this;console.log("精灵升级到:",t),this.playLevelUpAnimation(t),this.$message.success("恭喜！精灵升级到 Lv."+t+"！");var i=this.calculateFormByLevel(t),s=this.currentSpriteForm;i!==s&&setTimeout(function(){e.$message.info("精灵进化为"+e.getFormDescription(i)+"！")},1500)},calculateFormByLevel:function(t){return t<=5?1:t<=15?2:3},getFormDescription:function(t){return{1:"幼体形态",2:"成长形态",3:"完全体形态"}[t]||"未知形态"},checkGlowEffect:function(){var t=this;this.hasGlowEffect&&setTimeout(function(){t.playGlowEffect(),setInterval(function(){t.hasGlowEffect&&t.playGlowEffect()},1e4)},1e3)},goToSpriteSelection:function(){this.$router.push("/sprite-selection")},startCallDurationTimer:function(){var t=this;this.callDurationTimer=setInterval(function(){t.callState.isInCall&&t.$forceUpdate()},1e3)},stopCallDurationTimer:function(){this.callDurationTimer&&(clearInterval(this.callDurationTimer),this.callDurationTimer=null)},handleImageError:function(t){t.target.src=i("uVB+")}})},L={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"sprite-container"},[i("div",{staticClass:"sprite-card"},[i("div",{staticClass:"sprite-info-section"},[i("div",{staticClass:"level-display"},[i("span",{staticClass:"level-label"},[t._v("Lv.")]),t._v(" "),i("span",{staticClass:"level-number"},[t._v(t._s(t.currentLevel))]),t._v(" "),i("span",{staticClass:"form-indicator"},[t._v("("+t._s(t.formDescription)+")")])]),t._v(" "),i("div",{staticClass:"experience-section"},[i("ExperienceBar",{attrs:{current:t.experienceProgress.current,total:t.experienceProgress.total,percentage:t.experienceProgress.percentage,needed:t.experienceProgress.needed},on:{"level-up":t.handleLevelUp}})],1),t._v(" "),i("div",{staticClass:"sprite-display"},[i("div",{staticClass:"sprite-avatar-container"},[i("img",{staticClass:"sprite-avatar",class:{"glow-effect":t.hasGlowEffect,"breathe-animation":!t.isInteracting},attrs:{src:t.currentSpriteImage,alt:t.currentSpriteName},on:{error:t.handleImageError}}),t._v(" "),t.hasGlowEffect?i("div",{staticClass:"glow-particles"},t._l(8,function(t){return i("div",{key:t,staticClass:"particle"})}),0):t._e(),t._v(" "),i("div",{ref:"effectContainer",staticClass:"effect-container"})])]),t._v(" "),i("div",{staticClass:"sprite-name-section"},[i("SpriteNameEditor",{attrs:{name:t.currentSpriteName,editable:!0},on:{"name-change":t.handleNameChange}})],1)])]),t._v(" "),i("div",{staticClass:"sprite-card"},[i("div",{staticClass:"card-title"},[t._v("互动")]),t._v(" "),i("InteractionButtons",{attrs:{sprite:t.currentSprite,"can-feed":t.canFeedToday,"is-interacting":t.isInteracting},on:{feed:t.handleFeed,touch:t.handleTouch,bath:t.handleBath}})],1),t._v(" "),i("div",{staticClass:"sprite-card"},[i("div",{staticClass:"function-buttons"},[i("el-button",{staticClass:"function-btn rules-btn",on:{click:function(e){t.showUpgradeRules=!0}}},[i("i",{staticClass:"el-icon-question"}),t._v("\n        等级提升规则\n      ")]),t._v(" "),i("el-button",{staticClass:"function-btn change-sprite-btn",on:{click:t.goToSpriteSelection}},[i("i",{staticClass:"el-icon-refresh"}),t._v("\n        更换精灵\n      ")])],1)]),t._v(" "),t.callState.isInCall?i("div",{staticClass:"sprite-card call-status"},[t._m(0),t._v(" "),i("div",{staticClass:"call-info"},[i("p",{staticClass:"call-duration"},[t._v("通话时长: "+t._s(t.formatCallDuration))]),t._v(" "),i("p",{staticClass:"exp-gaining"},[t._v("正在获得经验值...")])])]):t._e(),t._v(" "),i("SpriteUpgradeRules",{attrs:{visible:t.showUpgradeRules},on:{close:function(e){t.showUpgradeRules=!1}}}),t._v(" "),t.isLoading?i("div",{staticClass:"loading-overlay"},[i("div",{staticClass:"loading-spinner"}),t._v(" "),i("p",[t._v("正在加载精灵信息...")])]):t._e()],1)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"card-title"},[e("i",{staticClass:"el-icon-phone"}),this._v("\n      通话中\n    ")])}]};var G=i("l2X3")(k,L,!1,function(t){i("d3Ty")},"data-v-a7372f7a",null);e.default=G.exports},d3Ty:function(t,e){},dPYC:function(t,e){},gFrU:function(t,e,i){"use strict";e.a=function(t){var e=(new Date).toDateString();if(!t||t!==e)return{canFeed:!0,willGetExp:!0,message:"可以投喂，将获得经验值奖励！"};return{canFeed:!0,willGetExp:!1,message:"今天已经投喂过了，只能看动画哦~"}},e.b=function(t){if(t<60)return t+"分钟";var e=Math.floor(t/60),i=t%60;if(0===i)return e+"小时";return e+"小时"+i+"分钟"},e.c=s;function s(t){return{1:"幼体形态",2:"成长形态",3:"完全体形态"}[t]||"未知形态"}},"uVB+":function(t,e){t.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMjAiIGZpbGw9IiNGNUY1RjUiLz4KICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSIzNSIgZmlsbD0iI0UwRTBFMCIvPgogIDxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjQiIGZpbGw9IiMzMzMiLz4KICA8Y2lyY2xlIGN4PSI3MCIgY3k9IjUwIiByPSI0IiBmaWxsPSIjMzMzIi8+CiAgPHBhdGggZD0iTTQ1IDc1UTYwIDg1IDc1IDc1IiBzdHJva2U9IiMzMzMiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgogIDx0ZXh0IHg9IjYwIiB5PSIxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgZmlsbD0iIzY2NiI+57K+54G1PC90ZXh0Pgo8L3N2Zz4K"}});
webpackJsonp([6],{"3xks":function(e,t,n){"use strict";var s=n("WF7N"),a=n.n(s),r=n("tnuy"),i=n.n(r),o=n("cUib"),c=n.n(o),l=new(function(){function e(){i()(this,e),this.activeAnimations=new a.a,this.animationQueue=[],this.isProcessingQueue=!1}return c()(e,[{key:"playFeedAnimation",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(e){var n="feed_"+Date.now();return this.addSpriteShakeAnimation(e,n),this.createDonutParticles(e,t),t&&this.showExpGainEffect(e,20),this.playSound("feed"),n}}},{key:"playLevelUpAnimation",value:function(e,t){if(e){var n="levelup_"+Date.now();return this.createLevelUpGlow(e),this.createStarBurstEffect(e),this.showLevelUpMessage(e,t),this.playSound("levelup"),n}}},{key:"playGlowEffect",value:function(e){if(e){var t="glow_"+Date.now();return e.classList.add("glow-animation"),this.createGlowParticles(e),this.activeAnimations.set(t,{element:e,type:"glow",startTime:Date.now()}),t}}},{key:"playTouchAnimation",value:function(e){if(e){var t="touch_"+Date.now();return this.addSpriteBounceAnimation(e,t),this.createHeartParticles(e),this.playSound("touch"),t}}},{key:"playBathAnimation",value:function(e){if(e){var t="bath_"+Date.now();return this.addSpriteShakeAnimation(e,t),this.createWaterDroplets(e),this.playSound("bath"),t}}},{key:"addSpriteShakeAnimation",value:function(e,t){var n=this;e.classList.add("feed-animation"),setTimeout(function(){e.classList.remove("feed-animation"),n.activeAnimations.delete(t)},600),this.activeAnimations.set(t,{element:e,type:"shake",startTime:Date.now()})}},{key:"addSpriteBounceAnimation",value:function(e,t){var n=this;e.classList.add("bounce-animation"),setTimeout(function(){e.classList.remove("bounce-animation"),n.activeAnimations.delete(t)},1e3),this.activeAnimations.set(t,{element:e,type:"bounce",startTime:Date.now()})}},{key:"createDonutParticles",value:function(e,t){for(var n=this.getOrCreateEffectContainer(e),s=t?5:3,a=function(e){var s=document.createElement("div");s.className="particle donut-particle",s.style.left=20+60*Math.random()+"%",s.style.animationDelay=.2*e+"s",s.style.backgroundColor=t?"#FFD93D":"#DDD",n.appendChild(s),setTimeout(function(){n.contains(s)&&n.removeChild(s)},1500)},r=0;r<s;r++)a(r)}},{key:"createHeartParticles",value:function(e){for(var t=this.getOrCreateEffectContainer(e),n=function(e){var n=document.createElement("div");n.className="particle heart-particle",n.style.left=15+70*Math.random()+"%",n.style.animationDelay=.3*e+"s",n.innerHTML="❤️",t.appendChild(n),setTimeout(function(){t.contains(n)&&t.removeChild(n)},2e3)},s=0;s<6;s++)n(s)}},{key:"createWaterDroplets",value:function(e){for(var t=this.getOrCreateEffectContainer(e),n=function(e){var n=document.createElement("div");n.className="particle water-droplet",n.style.left=10+80*Math.random()+"%",n.style.animationDelay=.15*e+"s",n.style.backgroundColor="#4ECDC4",t.appendChild(n),setTimeout(function(){t.contains(n)&&t.removeChild(n)},1200)},s=0;s<8;s++)n(s)}},{key:"createLevelUpGlow",value:function(e){var t=document.createElement("div");t.className="level-up-glow",t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.right="0",t.style.bottom="0",t.style.background="radial-gradient(circle, rgba(255,215,0,0.6) 0%, transparent 70%)",t.style.borderRadius="50%",t.style.animation="levelUpGlow 2s ease-out",t.style.pointerEvents="none";var n=this.getOrCreateEffectContainer(e);n.appendChild(t),setTimeout(function(){n.contains(t)&&n.removeChild(t)},2e3)}},{key:"createStarBurstEffect",value:function(e){for(var t=this.getOrCreateEffectContainer(e),n=function(e){var n=document.createElement("div");n.className="particle star-burst",n.innerHTML="⭐",n.style.position="absolute",n.style.left="50%",n.style.top="50%",n.style.transform="translate(-50%, -50%)",n.style.animation="starBurst 1.5s ease-out "+.1*e+"s",n.style.animationFillMode="forwards";var s=30*e;n.style.setProperty("--burst-angle",s+"deg"),t.appendChild(n),setTimeout(function(){t.contains(n)&&t.removeChild(n)},1500)},s=0;s<12;s++)n(s)}},{key:"createGlowParticles",value:function(e){for(var t=this.getOrCreateEffectContainer(e),n=function(e){var n=document.createElement("div");n.className="particle glow-particle",n.style.left=100*Math.random()+"%",n.style.top=100*Math.random()+"%",n.style.animationDelay=2*Math.random()+"s",n.style.backgroundColor="#FFD700",t.appendChild(n),setTimeout(function(){t.contains(n)&&t.removeChild(n)},3e3)},s=0;s<8;s++)n()}},{key:"showExpGainEffect",value:function(e,t){var n=document.createElement("div");n.className="exp-gain-indicator",n.textContent="+"+t+"EXP",n.style.position="absolute",n.style.top="-30px",n.style.right="10px",n.style.color="#4CAF50",n.style.fontWeight="bold",n.style.fontSize="14px",n.style.animation="expGainFloat 2s ease-out",n.style.pointerEvents="none",n.style.zIndex="1000";var s=this.getOrCreateEffectContainer(e);s.appendChild(n),setTimeout(function(){s.contains(n)&&s.removeChild(n)},2e3)}},{key:"showLevelUpMessage",value:function(e,t){var n=document.createElement("div");n.className="level-up-message",n.innerHTML='<i class="el-icon-trophy"></i>升级到 Lv.'+t+"！",n.style.position="absolute",n.style.top="-50px",n.style.left="50%",n.style.transform="translateX(-50%)",n.style.background="linear-gradient(45deg, #FFD700, #FFA500)",n.style.color="white",n.style.padding="8px 16px",n.style.borderRadius="20px",n.style.fontSize="16px",n.style.fontWeight="bold",n.style.animation="levelUpMessage 3s ease-out",n.style.pointerEvents="none",n.style.zIndex="1000";var s=this.getOrCreateEffectContainer(e);s.appendChild(n),setTimeout(function(){s.contains(n)&&s.removeChild(n)},3e3)}},{key:"getOrCreateEffectContainer",value:function(e){var t=e.querySelector(".effect-container");return t||((t=document.createElement("div")).className="effect-container",t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.right="0",t.style.bottom="0",t.style.pointerEvents="none",t.style.overflow="hidden",t.style.borderRadius="50%","static"===getComputedStyle(e).position&&(e.style.position="relative"),e.appendChild(t)),t}},{key:"playSound",value:function(e){console.log("播放音效:",e)}},{key:"stopAnimation",value:function(e){var t=this.activeAnimations.get(e);t&&(t.element.classList.remove("feed-animation","bounce-animation","glow-animation"),this.activeAnimations.delete(e))}},{key:"stopAllAnimations",value:function(){var e=this;this.activeAnimations.forEach(function(t,n){e.stopAnimation(n)})}},{key:"destroy",value:function(){this.stopAllAnimations(),this.animationQueue=[],this.isProcessingQueue=!1}}]),e}());t.a=l},"4nF9":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){t.default={},void 0!==e&&e.exports&&(e.exports={})}.call(t,n("3FBP")(e))},AK6W:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n.d(t,"STORAGE_KEYS",function(){return w}),n.d(t,"saveSpriteData",function(){return T}),n.d(t,"loadSpriteData",function(){return _}),n.d(t,"saveUserSettings",function(){return E}),n.d(t,"loadUserSettings",function(){return x}),n.d(t,"addCallRecord",function(){return A});var s=n("Ov2S"),a=n.n(s),r=n("l+io"),i=n.n(r),o=n("SaMe"),c=n.n(o),l=n("vvmr"),u=n.n(l),d=n("s/F+"),h=n.n(d),p=n("HkQL"),f=n.n(p),m=n("yrbl"),v=n.n(m),g=n("tnuy"),k=n.n(g),y=n("cUib"),b=n.n(y),w={SPRITE_DATA:"sprite_data",USER_SETTINGS:"user_settings",CALL_RECORDS:"call_records",SPRITE_ANIMATIONS:"sprite_animations"},S={user:{hasSprite:!1,currentSpriteId:null,unlockedSprites:[],totalCallTime:0,consecutiveCallDays:0,lastCallDate:null,lastFeedDate:null},sprites:{},version:"1.0.0"},C={soundEnabled:!0,animationEnabled:!0,notificationEnabled:!0,version:"1.0.0"},D=new(function(){function e(){k()(this,e),this.plugins=null,this.isInitialized=!1}return b()(e,[{key:"init",value:function(e){this.plugins=e,this.isInitialized=!0,console.log("数据存储管理器初始化完成")}},{key:"getInitializationStatus",value:function(){return this.isInitialized}},{key:"checkInitialized",value:function(){if(!this.isInitialized||!this.plugins)throw new Error("数据存储管理器未初始化，请先调用init方法")}},{key:"save",value:function(){var e=v()(h.a.mark(function e(t,n){var s,a;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.checkInitialized(),e.prev=1,s=f()(n),e.next=5,this.plugins.mediaPlugin.saveKeyValueData({key:t,value:s});case 5:if(1!==(a=e.sent).code){e.next=11;break}return console.log("数据保存成功: "+t),e.abrupt("return",!0);case 11:return console.error("数据保存失败: "+t,a.message),e.abrupt("return",!1);case 13:e.next=19;break;case 15:return e.prev=15,e.t0=e.catch(1),console.error("数据保存异常: "+t,e.t0),e.abrupt("return",!1);case 19:case"end":return e.stop()}},e,this,[[1,15]])}));return function(t,n){return e.apply(this,arguments)}}()},{key:"load",value:function(){var e=v()(h.a.mark(function e(t){var n,s,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.checkInitialized(),e.prev=1,e.next=4,this.plugins.mediaPlugin.getKeyValueData({key:t});case 4:if(1!==(n=e.sent).code||!n.data||!n.data.value){e.next=11;break}return s=JSON.parse(n.data.value),console.log("数据加载成功: "+t),e.abrupt("return",s);case 11:return console.log("数据不存在，使用默认值: "+t),e.abrupt("return",a);case 13:e.next=19;break;case 15:return e.prev=15,e.t0=e.catch(1),console.error("数据加载异常: "+t,e.t0),e.abrupt("return",a);case 19:case"end":return e.stop()}},e,this,[[1,15]])}));return function(t){return e.apply(this,arguments)}}()},{key:"remove",value:function(){var e=v()(h.a.mark(function e(t){var n;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.checkInitialized(),e.prev=1,e.next=4,this.plugins.mediaPlugin.saveKeyValueData({key:t,value:""});case 4:if(1!==(n=e.sent).code){e.next=10;break}return console.log("数据删除成功: "+t),e.abrupt("return",!0);case 10:return console.error("数据删除失败: "+t,n.message),e.abrupt("return",!1);case 12:e.next=18;break;case 14:return e.prev=14,e.t0=e.catch(1),console.error("数据删除异常: "+t,e.t0),e.abrupt("return",!1);case 18:case"end":return e.stop()}},e,this,[[1,14]])}));return function(t){return e.apply(this,arguments)}}()},{key:"exists",value:function(){var e=v()(h.a.mark(function e(t){var n;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.checkInitialized(),e.prev=1,e.next=4,this.plugins.mediaPlugin.getKeyValueData({key:t});case 4:return n=e.sent,e.abrupt("return",1===n.code&&n.data&&n.data.value&&""!==n.data.value);case 8:return e.prev=8,e.t0=e.catch(1),console.error("检查数据存在异常: "+t,e.t0),e.abrupt("return",!1);case 12:case"end":return e.stop()}},e,this,[[1,8]])}));return function(t){return e.apply(this,arguments)}}()},{key:"saveSpriteData",value:function(){var e=v()(h.a.mark(function e(t){var n;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=u()({},t,{lastUpdated:Date.now()}),e.next=3,this.save(w.SPRITE_DATA,n);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"loadSpriteData",value:function(){var e=v()(h.a.mark(function e(){var t;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.load(w.SPRITE_DATA,S);case 2:if((t=e.sent).version&&t.version===S.version){e.next=6;break}return console.log("检测到数据版本不匹配，进行数据迁移"),e.abrupt("return",this.migrateSpriteData(t));case 6:return e.abrupt("return",t);case 7:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"saveUserSettings",value:function(){var e=v()(h.a.mark(function e(t){return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.save(w.USER_SETTINGS,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"loadUserSettings",value:function(){var e=v()(h.a.mark(function e(){return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.load(w.USER_SETTINGS,C);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"saveCallRecords",value:function(){var e=v()(h.a.mark(function e(t){return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.save(w.CALL_RECORDS,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"loadCallRecords",value:function(){var e=v()(h.a.mark(function e(){return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.load(w.CALL_RECORDS,[]);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"addCallRecord",value:function(){var e=v()(h.a.mark(function e(t){var n;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.loadCallRecords();case 3:return(n=e.sent).push(u()({},t,{id:Date.now(),timestamp:Date.now()})),n.length>100&&n.splice(0,n.length-100),e.next=8,this.saveCallRecords(n);case 8:return e.abrupt("return",e.sent);case 11:return e.prev=11,e.t0=e.catch(0),console.error("添加通话记录失败:",e.t0),e.abrupt("return",!1);case 15:case"end":return e.stop()}},e,this,[[0,11]])}));return function(t){return e.apply(this,arguments)}}()},{key:"migrateSpriteData",value:function(e){var t=u()({},S);return e.user&&(t.user=u()({},t.user,e.user)),e.sprites&&(t.sprites=e.sprites),console.log("数据迁移完成"),t}},{key:"clearAllData",value:function(){var e=v()(h.a.mark(function e(){var t,n,s,a=this;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t=c()(w),e.next=4,i.a.all(t.map(function(e){return a.remove(e)}));case 4:return n=e.sent,(s=n.every(function(e){return!0===e}))?console.log("所有数据清空成功"):console.error("部分数据清空失败"),e.abrupt("return",s);case 10:return e.prev=10,e.t0=e.catch(0),console.error("清空数据异常:",e.t0),e.abrupt("return",!1);case 14:case"end":return e.stop()}},e,this,[[0,10]])}));return function(){return e.apply(this,arguments)}}()},{key:"getStorageInfo",value:function(){var e=v()(h.a.mark(function e(){var t,n,s,r,i,o,l,u,d;return h.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,t=c()(w),n={totalKeys:t.length,existingKeys:0,keyDetails:{}},s=!0,r=!1,i=void 0,e.prev=6,o=a()(t);case 8:if(s=(l=o.next()).done){e.next=25;break}return u=l.value,e.next=12,this.exists(u);case 12:if(!e.sent){e.next=21;break}return n.existingKeys++,e.next=17,this.load(u);case 17:d=e.sent,n.keyDetails[u]={exists:!0,size:f()(d).length,lastUpdated:d.lastUpdated||null},e.next=22;break;case 21:n.keyDetails[u]={exists:!1,size:0,lastUpdated:null};case 22:s=!0,e.next=8;break;case 25:e.next=31;break;case 27:e.prev=27,e.t0=e.catch(6),r=!0,i=e.t0;case 31:e.prev=31,e.prev=32,!s&&o.return&&o.return();case 34:if(e.prev=34,!r){e.next=37;break}throw i;case 37:return e.finish(34);case 38:return e.finish(31);case 39:return e.abrupt("return",n);case 42:return e.prev=42,e.t1=e.catch(0),console.error("获取存储信息异常:",e.t1),e.abrupt("return",null);case 46:case"end":return e.stop()}},e,this,[[0,42],[6,27,31,39],[32,,34,38]])}));return function(){return e.apply(this,arguments)}}()}]),e}());t.default=D;var T=function(e){return D.saveSpriteData(e)},_=function(){return D.loadSpriteData()},E=function(e){return D.saveUserSettings(e)},x=function(){return D.loadUserSettings()},A=function(e){return D.addCallRecord(e)}},IcnI:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=n("4jgH"),a=n("nVSy"),r=n("vvmr"),i=n.n(r),o=n("s/F+"),c=n.n(o),l=n("yrbl"),u=n.n(l),d=n("6uKG"),h=n.n(d),p={user:{hasSprite:!1,currentSpriteId:null,unlockedSprites:[],totalCallTime:0,consecutiveCallDays:0,lastCallDate:null,lastFeedDate:null},sprites:{},spriteTypes:[{id:1,name:"精灵类型1",unlockRequirement:0,forms:{1:{level:[1,5],image:"sprite1_form1.png"},2:{level:[6,15],image:"sprite1_form2.png"},3:{level:[16,99],image:"sprite1_form3.png"}}},{id:2,name:"精灵类型2",unlockRequirement:60,forms:{1:{level:[1,5],image:"sprite2_form1.png"},2:{level:[6,15],image:"sprite2_form2.png"},3:{level:[16,99],image:"sprite2_form3.png"}}}],callState:{isInCall:!1,callStartTime:null,currentCallDuration:0},callDurationTimer:null},f={namespaced:!0,state:p,getters:{currentSprite:function(e){return e.user.currentSpriteId?e.sprites[e.user.currentSpriteId]:null},currentSpriteLevel:function(e,t){var n=t.currentSprite;return n?Math.floor(n.experience/50)+1:1},currentSpriteForm:function(e,t){var n=t.currentSpriteLevel;return n<=5?1:n<=15?2:3},experienceProgress:function(e,t){var n=t.currentSprite;if(!n)return{current:0,needed:50,percentage:0};var s=n.experience%50;return{current:s,needed:50-s,percentage:s/50*100}},unlockedSpriteTypes:function(e){return e.spriteTypes.filter(function(t){return e.user.totalCallTime>=t.unlockRequirement})},hasGlowEffect:function(e){return e.user.consecutiveCallDays>=3}},mutations:{SET_USER_SPRITE_STATUS:function(e,t){e.user.hasSprite=t},SET_CURRENT_SPRITE:function(e,t){e.user.currentSpriteId=t},ADD_SPRITE:function(e,t){s.default.set(e.sprites,t.id,t)},UPDATE_SPRITE_EXPERIENCE:function(e,t){var n=t.spriteId,s=t.experience;e.sprites[n]&&(e.sprites[n].experience=s)},UPDATE_SPRITE_NAME:function(e,t){var n=t.spriteId,s=t.name;e.sprites[n]&&(e.sprites[n].name=s)},UPDATE_TOTAL_CALL_TIME:function(e,t){e.user.totalCallTime+=t},SET_CALL_STATE:function(e,t){var n=t.isInCall,s=t.startTime,a=void 0===s?null:s;e.callState.isInCall=n,e.callState.callStartTime=a,n||(e.callState.currentCallDuration=0)},UPDATE_CURRENT_CALL_DURATION:function(e,t){e.callState.currentCallDuration=t},UPDATE_CONSECUTIVE_CALL_DAYS:function(e,t){e.user.consecutiveCallDays=t},SET_LAST_CALL_DATE:function(e,t){e.user.lastCallDate=t},SET_LAST_FEED_DATE:function(e,t){e.user.lastFeedDate=t},ADD_UNLOCKED_SPRITE:function(e,t){e.user.unlockedSprites.includes(t)||e.user.unlockedSprites.push(t)},SET_CALL_DURATION_TIMER:function(e,t){e.callDurationTimer=t},LOAD_STATE:function(e,t){h()(e,t)}},actions:{initializeSpriteData:function(e){var t=this,n=(e.commit,e.dispatch);return u()(c.a.mark(function e(){return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n("loadSpriteData");case 3:return e.next=5,n("initializeCallMonitoring");case 5:e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("初始化精灵数据失败:",e.t0);case 10:case"end":return e.stop()}},e,t,[[0,7]])}))()},initializeCallMonitoring:function(e){var t=this,n=e.dispatch;return u()(c.a.mark(function e(){var s;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,n("getPlugins");case 3:if((s=e.sent)&&s.callbackPlugin){e.next=7;break}return console.warn("无法获取通话插件，跳过通话监听初始化"),e.abrupt("return");case 7:s.callbackPlugin.callStateNotified(function(e){console.log("通话状态变化:",e),n("handleCallStateChange",e)}),console.log("通话监听初始化完成"),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("通话监听初始化失败:",e.t0);case 14:case"end":return e.stop()}},e,t,[[0,11]])}))()},getPlugins:function(){var e=this;return u()(c.a.mark(function t(){var n;return c.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!window.plugins){t.next=2;break}return t.abrupt("return",window.plugins);case 2:if(!(e._vm&&e._vm.$root&&e._vm.$root.$children[0])){t.next=6;break}if(!(n=e._vm.$root.$children[0]).plugins){t.next=6;break}return t.abrupt("return",n.plugins);case 6:throw new Error("无法获取plugins对象");case 7:case"end":return t.stop()}},t,e)}))()},handleCallStateChange:function(e,t){var n=this,s=(e.commit,e.state,e.getters,e.dispatch);return u()(c.a.mark(function e(){var a;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,"CONNECTED"!==(a=t.callInfo&&t.callInfo.callState)){e.next=7;break}return e.next=5,s("startCallTimer");case 5:e.next=10;break;case 7:if("DISCONNECTED"!==a&&"FAILED"!==a){e.next=10;break}return e.next=10,s("endCallTimer");case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),console.error("处理通话状态变化失败:",e.t0);case 15:case"end":return e.stop()}},e,n,[[0,12]])}))()},claimSprite:function(e,t){var n=this,s=e.commit,a=e.state;return u()(c.a.mark(function e(){var r;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a.spriteTypes.find(function(e){return e.id===t})){e.next=3;break}throw new Error("精灵类型不存在");case 3:return r={id:"sprite_"+t+"_"+Date.now(),typeId:t,name:"通话精灵",level:1,experience:0,form:1,hasGlowEffect:!1,unlockTime:Date.now()},s("ADD_SPRITE",r),s("SET_CURRENT_SPRITE",r.id),s("SET_USER_SPRITE_STATUS",!0),s("ADD_UNLOCKED_SPRITE",t),e.next=10,n.dispatch("sprite/saveSpriteData");case 10:return e.abrupt("return",r);case 11:case"end":return e.stop()}},e,n)}))()},switchSprite:function(e,t){var n=this,s=e.commit,a=e.dispatch;return u()(c.a.mark(function e(){return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s("SET_CURRENT_SPRITE",t),e.next=3,a("saveSpriteData");case 3:case"end":return e.stop()}},e,n)}))()},feedSprite:function(e){var t=this,n=e.commit,s=e.state,a=e.getters,r=e.dispatch;return u()(c.a.mark(function e(){var i,o,l;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=a.currentSprite){e.next=3;break}throw new Error("没有当前精灵");case 3:if(o=(new Date).toDateString(),s.user.lastFeedDate!==o){e.next=7;break}return e.abrupt("return",{success:!1,message:"今天已经投喂过了"});case 7:return l=i.experience+20,n("UPDATE_SPRITE_EXPERIENCE",{spriteId:i.id,experience:l}),n("SET_LAST_FEED_DATE",o),e.next=12,r("saveSpriteData");case 12:return e.abrupt("return",{success:!0,message:"投喂成功，获得20经验值！"});case 13:case"end":return e.stop()}},e,t)}))()},startCallTimer:function(e){var t=e.commit,n=e.dispatch,s=Date.now();t("SET_CALL_STATE",{isInCall:!0,startTime:s}),n("startCallDurationUpdate"),console.log("开始通话计时:",new Date(s))},endCallTimer:function(e){var t=this,n=e.commit,s=e.state,a=e.getters,r=e.dispatch;return u()(c.a.mark(function e(){var i,o,l,u,d;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(s.callState.isInCall){e.next=2;break}return e.abrupt("return");case 2:if(i=Date.now(),o=Math.floor((i-s.callState.callStartTime)/6e4),console.log("结束通话计时，通话时长:",o,"分钟"),r("stopCallDurationUpdate"),!(o>0&&a.currentSprite)){e.next=20;break}return n("UPDATE_TOTAL_CALL_TIME",o),l=a.currentSprite,u=10*o,d=l.experience+u,console.log("通话获得经验值:",u),n("UPDATE_SPRITE_EXPERIENCE",{spriteId:l.id,experience:d}),e.next=15,r("updateConsecutiveCallDays");case 15:return e.next=17,r("checkNewSpriteUnlock");case 17:return e.next=19,r("saveSpriteData");case 19:r("notifyExperienceGain",u);case 20:n("SET_CALL_STATE",{isInCall:!1});case 21:case"end":return e.stop()}},e,t)}))()},startCallDurationUpdate:function(e){var t=e.commit,n=e.state;n.callDurationTimer&&clearInterval(n.callDurationTimer);var s=setInterval(function(){if(n.callState.isInCall&&n.callState.callStartTime){var e=Math.floor((Date.now()-n.callState.callStartTime)/1e3);t("UPDATE_CURRENT_CALL_DURATION",e)}},1e3);t("SET_CALL_DURATION_TIMER",s)},stopCallDurationUpdate:function(e){var t=e.commit,n=e.state;n.callDurationTimer&&(clearInterval(n.callDurationTimer),t("SET_CALL_DURATION_TIMER",null))},notifyExperienceGain:function(e,t){console.log("经验值获得通知:",t)},updateSpriteName:function(e,t){var n=this,s=e.commit,a=e.dispatch,r=t.spriteId,i=t.name;return u()(c.a.mark(function e(){return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,r&&i){e.next=3;break}throw new Error("精灵ID和名字不能为空");case 3:if(!(i.length>10)){e.next=5;break}throw new Error("名字长度不能超过10个字符");case 5:if(0!==i.trim().length){e.next=7;break}throw new Error("名字不能为空");case 7:return s("UPDATE_SPRITE_NAME",{spriteId:r,name:i.trim()}),e.next=10,a("saveSpriteData");case 10:return console.log("精灵名字更新成功:",i),e.abrupt("return",!0);case 14:throw e.prev=14,e.t0=e.catch(0),console.error("更新精灵名字失败:",e.t0),e.t0;case 18:case"end":return e.stop()}},e,n,[[0,14]])}))()},updateConsecutiveCallDays:function(e){var t=e.commit,n=e.state,s=(new Date).toDateString(),a=n.user.lastCallDate;if(a){var r=new Date(a),i=new Date(s).getTime()-r.getTime(),o=Math.ceil(i/864e5);1===o?t("UPDATE_CONSECUTIVE_CALL_DAYS",n.user.consecutiveCallDays+1):o>1&&t("UPDATE_CONSECUTIVE_CALL_DAYS",1)}else t("UPDATE_CONSECUTIVE_CALL_DAYS",1);t("SET_LAST_CALL_DATE",s)},checkNewSpriteUnlock:function(e){var t=e.commit,n=e.state;n.spriteTypes.forEach(function(e){n.user.totalCallTime>=e.unlockRequirement&&!n.user.unlockedSprites.includes(e.id)&&t("ADD_UNLOCKED_SPRITE",e.id)})},saveSpriteData:function(e){var t=this,s=e.state;return u()(c.a.mark(function e(){var a,r;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise(function(e){e()}).then(n.bind(null,"AK6W"));case 3:if((a=e.sent.default).getInitializationStatus()){e.next=7;break}return console.warn("数据存储未初始化，跳过保存"),e.abrupt("return",!1);case 7:return r={user:s.user,sprites:s.sprites,spriteTypes:s.spriteTypes,lastSaved:Date.now(),version:"1.0.0"},e.next=10,a.saveSpriteData(r);case 10:if(e.sent){e.next=13;break}throw new Error("数据保存失败");case 13:return console.log("精灵数据保存成功"),e.abrupt("return",!0);case 17:return e.prev=17,e.t0=e.catch(0),console.error("保存精灵数据失败:",e.t0),e.abrupt("return",!1);case 21:case"end":return e.stop()}},e,t,[[0,17]])}))()},loadSpriteData:function(e){var t=this,s=e.commit,a=e.state;return u()(c.a.mark(function e(){var r,o;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise(function(e){e()}).then(n.bind(null,"AK6W"));case 3:if((r=e.sent.default).getInitializationStatus()){e.next=7;break}return console.warn("数据存储未初始化，使用默认数据"),e.abrupt("return");case 7:return e.next=9,r.loadSpriteData();case 9:(o=e.sent)&&o.user?("1.0.0"!==o.version&&console.log("检测到数据版本差异，进行数据迁移"),s("LOAD_STATE",{user:i()({},a.user,o.user),sprites:o.sprites||{},spriteTypes:o.spriteTypes||a.spriteTypes,callState:a.callState,callDurationTimer:a.callDurationTimer}),console.log("精灵数据加载成功")):console.log("没有找到保存的精灵数据，使用默认数据"),e.next=16;break;case 13:e.prev=13,e.t0=e.catch(0),console.error("加载精灵数据失败:",e.t0);case 16:case"end":return e.stop()}},e,t,[[0,13]])}))()},backupSpriteData:function(e){var t=this,s=e.state;return u()(c.a.mark(function e(){var a,r,o;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise(function(e){e()}).then(n.bind(null,"AK6W"));case 3:return a=e.sent.default,r=i()({},s,{backupTime:Date.now(),backupVersion:"1.0.0"}),e.next=7,a.save("sprite_backup",r);case 7:return(o=e.sent)&&console.log("精灵数据备份成功"),e.abrupt("return",o);case 12:return e.prev=12,e.t0=e.catch(0),console.error("备份精灵数据失败:",e.t0),e.abrupt("return",!1);case 16:case"end":return e.stop()}},e,t,[[0,12]])}))()},restoreSpriteData:function(e){var t=this,s=e.commit;return u()(c.a.mark(function e(){var a,r;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise(function(e){e()}).then(n.bind(null,"AK6W"));case 3:return a=e.sent.default,e.next=6,a.load("sprite_backup");case 6:if(!(r=e.sent)||!r.user){e.next=13;break}return s("LOAD_STATE",{user:r.user,sprites:r.sprites||{},spriteTypes:r.spriteTypes||p.spriteTypes}),console.log("精灵数据恢复成功"),e.abrupt("return",!0);case 13:return console.log("没有找到备份数据"),e.abrupt("return",!1);case 15:e.next=21;break;case 17:return e.prev=17,e.t0=e.catch(0),console.error("恢复精灵数据失败:",e.t0),e.abrupt("return",!1);case 21:case"end":return e.stop()}},e,t,[[0,17]])}))()},clearAllSpriteData:function(e){var t=this,s=e.commit;return u()(c.a.mark(function e(){var a;return c.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise(function(e){e()}).then(n.bind(null,"AK6W"));case 3:return a=e.sent.default,e.next=6,a.remove("sprite_data");case 6:return e.next=8,a.remove("sprite_backup");case 8:return s("LOAD_STATE",{user:{hasSprite:!1,currentSpriteId:null,unlockedSprites:[],totalCallTime:0,consecutiveCallDays:0,lastCallDate:null,lastFeedDate:null},sprites:{},spriteTypes:p.spriteTypes}),console.log("所有精灵数据已清除"),e.abrupt("return",!0);case 13:return e.prev=13,e.t0=e.catch(0),console.error("清除精灵数据失败:",e.t0),e.abrupt("return",!1);case 17:case"end":return e.stop()}},e,t,[[0,13]])}))()},handlePeerSyncEvent:function(e,t){e.commit;var n=t.eventType,s=t.data;switch(console.log("处理对方同步事件:",n,s),n){case"peer_experience_update":case"peer_name_update":case"peer_sprite_switch":case"peer_call_time_update":case"peer_feed_update":break;default:console.log("未处理的对方同步事件:",n)}}}};s.default.use(a.a);var m=new a.a.Store({modules:{sprite:f},strict:!1});t.default=m},NHnr:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=n("4jgH"),a=n("l+io"),r=n.n(a),i=n("HkQL"),o=n.n(i),c=n("s/F+"),u=n.n(c),d=n("yrbl"),h=n.n(d),p=n("4nF9"),f=n("tnuy"),m=n.n(f),v=n("cUib"),g=n.n(v),k=n("IcnI"),y=new(function(){function e(){m()(this,e),this.isTracking=!1,this.startTime=null,this.pausedTime=0,this.callbacks={onStart:[],onEnd:[],onUpdate:[]},this.updateInterval=null}return g()(e,[{key:"start",value:function(){var e=this;this.isTracking?console.warn("通话时长追踪已经在进行中"):(this.isTracking=!0,this.startTime=Date.now(),this.pausedTime=0,this.callbacks.onStart.forEach(function(t){try{t(e.startTime)}catch(e){console.error("通话开始回调执行错误:",e)}}),this.startUpdateTimer(),console.log("开始追踪通话时长:",new Date(this.startTime)))}},{key:"end",value:function(){if(!this.isTracking)return console.warn("通话时长追踪未在进行中"),null;var e=Date.now(),t=this.getCurrentDuration();this.isTracking=!1,this.stopUpdateTimer();var n={startTime:this.startTime,endTime:e,duration:t,durationMinutes:Math.floor(t/6e4),durationSeconds:Math.floor(t/1e3)};return this.callbacks.onEnd.forEach(function(e){try{e(n)}catch(e){console.error("通话结束回调执行错误:",e)}}),console.log("结束追踪通话时长:",n),this.reset(),n}},{key:"pause",value:function(){this.isTracking&&(this.pausedTime+=Date.now()-this.startTime,this.stopUpdateTimer(),console.log("暂停通话时长追踪"))}},{key:"resume",value:function(){this.isTracking&&(this.startTime=Date.now(),this.startUpdateTimer(),console.log("恢复通话时长追踪"))}},{key:"getCurrentDuration",value:function(){return this.isTracking?Date.now()-this.startTime+this.pausedTime:0}},{key:"getCurrentDurationMinutes",value:function(){return Math.floor(this.getCurrentDuration()/6e4)}},{key:"getCurrentDurationSeconds",value:function(){return Math.floor(this.getCurrentDuration()/1e3)}},{key:"formatDuration",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=null!==e?e:this.getCurrentDuration(),n=Math.floor(t/1e3),s=Math.floor(n/3600),a=Math.floor(n%3600/60),r=n%60;return s>0?s.toString().padStart(2,"0")+":"+a.toString().padStart(2,"0")+":"+r.toString().padStart(2,"0"):a.toString().padStart(2,"0")+":"+r.toString().padStart(2,"0")}},{key:"isActive",value:function(){return this.isTracking}},{key:"reset",value:function(){this.isTracking=!1,this.startTime=null,this.pausedTime=0,this.stopUpdateTimer()}},{key:"on",value:function(e,t){this.callbacks[e]&&"function"==typeof t?this.callbacks[e].push(t):console.warn("无效的事件类型或回调函数: "+e)}},{key:"off",value:function(e,t){if(this.callbacks[e]){var n=this.callbacks[e].indexOf(t);n>-1&&this.callbacks[e].splice(n,1)}}},{key:"startUpdateTimer",value:function(){var e=this;this.stopUpdateTimer(),this.updateInterval=setInterval(function(){var t=e.getCurrentDuration();e.callbacks.onUpdate.forEach(function(n){try{n({duration:t,durationMinutes:Math.floor(t/6e4),durationSeconds:Math.floor(t/1e3),formatted:e.formatDuration(t)})}catch(e){console.error("通话更新回调执行错误:",e)}})},1e3)}},{key:"stopUpdateTimer",value:function(){this.updateInterval&&(clearInterval(this.updateInterval),this.updateInterval=null)}},{key:"getStatus",value:function(){return{isTracking:this.isTracking,startTime:this.startTime,currentDuration:this.getCurrentDuration(),currentDurationMinutes:this.getCurrentDurationMinutes(),currentDurationSeconds:this.getCurrentDurationSeconds(),formatted:this.formatDuration()}}}]),e}());var b=new(function(){function e(){m()(this,e),this.isInitialized=!1,this.plugins=null,this.callStateCallback=null}return g()(e,[{key:"init",value:function(){var e=h()(u.a.mark(function e(t){var n=this;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isInitialized){e.next=3;break}return console.warn("通话监听已经初始化"),e.abrupt("return");case 3:if(this.plugins=t,t&&t.callbackPlugin){e.next=6;break}throw new Error("无法获取通话插件");case 6:e.prev=6,this.callStateCallback=function(e){n.handleCallStateChange(e)},t.callbackPlugin.callStateNotified(this.callStateCallback),this.setupCallTimeTrackerCallbacks(),this.isInitialized=!0,console.log("通话监听初始化成功"),e.next=18;break;case 14:throw e.prev=14,e.t0=e.catch(6),console.error("通话监听初始化失败:",e.t0),e.t0;case 18:case"end":return e.stop()}},e,this,[[6,14]])}));return function(t){return e.apply(this,arguments)}}()},{key:"setupCallTimeTrackerCallbacks",value:function(){var e=this;y.on("start",function(e){console.log("通话开始:",new Date(e)),k.default.dispatch("sprite/startCallTimer")}),y.on("end",function(t){console.log("通话结束:",t),k.default.dispatch("sprite/endCallTimer"),t.durationMinutes>0&&e.recordCallHistory(t)}),y.on("update",function(e){k.default.commit("sprite/UPDATE_CURRENT_CALL_DURATION",e.durationSeconds)})}},{key:"handleCallStateChange",value:function(e){try{console.log("收到通话状态变化:",e);var t=e.callInfo&&e.callInfo.callState;e.callInfo&&e.callInfo.callId;if(!t)return void console.warn("无效的通话状态数据");switch(t){case"CONNECTED":this.handleCallConnected(e);break;case"DISCONNECTED":this.handleCallDisconnected(e);break;case"FAILED":this.handleCallFailed(e);break;case"RINGING":this.handleCallRinging(e);break;default:console.log("未处理的通话状态:",t)}}catch(e){console.error("处理通话状态变化失败:",e)}}},{key:"handleCallConnected",value:function(e){console.log("通话已连接"),k.default.getters["sprite/currentSprite"]?y.start():console.log("用户没有精灵，不开始计时")}},{key:"handleCallDisconnected",value:function(e){console.log("通话已断开"),y.isActive()&&y.end()}},{key:"handleCallFailed",value:function(e){console.log("通话失败"),y.isActive()&&y.end()}},{key:"handleCallRinging",value:function(e){console.log("通话响铃中")}},{key:"recordCallHistory",value:function(){var e=h()(u.a.mark(function e(t){var s,a;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise(function(e){e()}).then(n.bind(null,"AK6W"));case 3:return s=e.sent.default,a={startTime:t.startTime,endTime:t.endTime,duration:t.duration,durationMinutes:t.durationMinutes,experienceGained:10*t.durationMinutes,spriteId:k.default.getters["sprite/currentSprite"]&&k.default.getters["sprite/currentSprite"].id,timestamp:Date.now()},e.next=7,s.addCallRecord(a);case 7:console.log("通话记录已保存:",a),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("保存通话记录失败:",e.t0);case 13:case"end":return e.stop()}},e,this,[[0,10]])}));return function(t){return e.apply(this,arguments)}}()},{key:"getCurrentCallState",value:function(){return{isInCall:y.isActive(),duration:y.getCurrentDuration(),durationMinutes:y.getCurrentDurationMinutes(),durationSeconds:y.getCurrentDurationSeconds(),formatted:y.formatDuration()}}},{key:"startTestCall",value:function(){this.isInitialized?(console.log("开始测试通话"),this.handleCallConnected({callInfo:{callState:"CONNECTED",callId:"test"}})):console.error("通话监听未初始化")}},{key:"endTestCall",value:function(){this.isInitialized?(console.log("结束测试通话"),this.handleCallDisconnected({callInfo:{callState:"DISCONNECTED",callId:"test"}})):console.error("通话监听未初始化")}},{key:"destroy",value:function(){if(y.isActive()&&y.end(),this.plugins&&this.plugins.callbackPlugin&&this.callStateCallback)try{this.plugins.callbackPlugin.removeCallStateCallback&&this.plugins.callbackPlugin.removeCallStateCallback(this.callStateCallback)}catch(e){console.warn("移除通话状态回调失败:",e)}this.isInitialized=!1,this.plugins=null,this.callStateCallback=null,console.log("通话监听已销毁")}}]),e}()),w=b,S=n("AK6W"),C=n("vvmr"),D=n.n(C),T=new(function(){function e(){m()(this,e),this.isInitialized=!1,this.plugins=null,this.dcLabel="sprite_sync",this.syncInterval=null,this.lastSyncTime=0,this.syncQueue=[],this.isProcessingSyncQueue=!1}return g()(e,[{key:"init",value:function(){var e=h()(u.a.mark(function e(t){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isInitialized){e.next=3;break}return console.warn("精灵同步管理器已经初始化"),e.abrupt("return");case 3:if(this.plugins=t,t&&t.dcPlugin){e.next=6;break}throw new Error("无法获取DC插件");case 6:e.prev=6,this.registerMessageListener(),this.startPeriodicSync(),this.isInitialized=!0,console.log("精灵同步管理器初始化成功"),e.next=17;break;case 13:throw e.prev=13,e.t0=e.catch(6),console.error("精灵同步管理器初始化失败:",e.t0),e.t0;case 17:case"end":return e.stop()}},e,this,[[6,13]])}));return function(t){return e.apply(this,arguments)}}()},{key:"registerMessageListener",value:function(){var e=this;k.default.subscribe(function(t,n){e.shouldSyncMutation(t)&&e.queueSync(t,n)})}},{key:"shouldSyncMutation",value:function(e){return["sprite/UPDATE_SPRITE_EXPERIENCE","sprite/UPDATE_SPRITE_NAME","sprite/SET_CURRENT_SPRITE","sprite/UPDATE_TOTAL_CALL_TIME","sprite/SET_LAST_FEED_DATE"].includes(e.type)}},{key:"queueSync",value:function(e,t){var n=this.createSyncData(e,t);n&&(this.syncQueue.push(n),this.processSyncQueue())}},{key:"createSyncData",value:function(e,t){var n=k.default.getters["sprite/currentSprite"];if(!n)return null;var s={timestamp:Date.now(),mutationType:e.type,spriteId:n.id,userId:this.getUserId()};switch(e.type){case"sprite/UPDATE_SPRITE_EXPERIENCE":return D()({},s,{type:"experience_update",data:{experience:e.payload.experience,level:Math.floor(e.payload.experience/50)+1}});case"sprite/UPDATE_SPRITE_NAME":return D()({},s,{type:"name_update",data:{name:e.payload.name}});case"sprite/SET_CURRENT_SPRITE":return D()({},s,{type:"sprite_switch",data:{newSpriteId:e.payload,spriteInfo:t.sprites[e.payload]}});case"sprite/UPDATE_TOTAL_CALL_TIME":return D()({},s,{type:"call_time_update",data:{totalCallTime:t.user.totalCallTime,consecutiveCallDays:t.user.consecutiveCallDays}});case"sprite/SET_LAST_FEED_DATE":return D()({},s,{type:"feed_update",data:{lastFeedDate:e.payload,spriteExperience:n.experience}});default:return null}}},{key:"processSyncQueue",value:function(){var e=h()(u.a.mark(function e(){var t;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.isProcessingSyncQueue&&0!==this.syncQueue.length){e.next=2;break}return e.abrupt("return");case 2:this.isProcessingSyncQueue=!0,e.prev=3;case 4:if(!(this.syncQueue.length>0)){e.next=12;break}return t=this.syncQueue.shift(),e.next=8,this.sendSyncData(t);case 8:return e.next=10,this.delay(100);case 10:e.next=4;break;case 12:e.next=17;break;case 14:e.prev=14,e.t0=e.catch(3),console.error("处理同步队列失败:",e.t0);case 17:return e.prev=17,this.isProcessingSyncQueue=!1,e.finish(17);case 20:case"end":return e.stop()}},e,this,[[3,14,17,20]])}));return function(){return e.apply(this,arguments)}}()},{key:"sendSyncData",value:function(){var e=h()(u.a.mark(function e(t){var n,s;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,this.plugins&&this.plugins.dcPlugin){e.next=4;break}return console.warn("DC插件不可用，跳过同步"),e.abrupt("return");case 4:return n=o()(D()({version:"1.0.0",messageType:"sprite_sync"},t)),e.next=7,this.plugins.dcPlugin.sendData({dcLabel:this.dcLabel,data:n});case 7:1===(s=e.sent).code?(console.log("精灵状态同步发送成功:",t.type),this.lastSyncTime=Date.now()):console.error("精灵状态同步发送失败:",s.message),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("发送同步数据失败:",e.t0);case 14:case"end":return e.stop()}},e,this,[[0,11]])}));return function(t){return e.apply(this,arguments)}}()},{key:"handleReceivedSyncMessage",value:function(){var e=h()(u.a.mark(function e(t){var n;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,"sprite_sync"===(n=JSON.parse(t)).messageType){e.next=4;break}return e.abrupt("return");case 4:if(console.log("收到精灵同步消息:",n.type),n.userId!==this.getUserId()){e.next=7;break}return e.abrupt("return");case 7:e.t0=n.type,e.next="experience_update"===e.t0?10:"name_update"===e.t0?13:"sprite_switch"===e.t0?16:"call_time_update"===e.t0?19:"feed_update"===e.t0?22:25;break;case 10:return e.next=12,this.handleExperienceUpdate(n);case 12:return e.abrupt("break",26);case 13:return e.next=15,this.handleNameUpdate(n);case 15:return e.abrupt("break",26);case 16:return e.next=18,this.handleSpriteSwitch(n);case 18:return e.abrupt("break",26);case 19:return e.next=21,this.handleCallTimeUpdate(n);case 21:return e.abrupt("break",26);case 22:return e.next=24,this.handleFeedUpdate(n);case 24:return e.abrupt("break",26);case 25:console.log("未知的同步消息类型:",n.type);case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(0),console.error("处理同步消息失败:",e.t1);case 31:case"end":return e.stop()}},e,this,[[0,28]])}));return function(t){return e.apply(this,arguments)}}()},{key:"handleExperienceUpdate",value:function(){var e=h()(u.a.mark(function e(t){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("对方精灵经验值更新:",t.data),this.emitSyncEvent("peer_experience_update",t.data);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"handleNameUpdate",value:function(){var e=h()(u.a.mark(function e(t){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("对方精灵名字更新:",t.data.name),this.emitSyncEvent("peer_name_update",t.data);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"handleSpriteSwitch",value:function(){var e=h()(u.a.mark(function e(t){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("对方切换精灵:",t.data),this.emitSyncEvent("peer_sprite_switch",t.data);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"handleCallTimeUpdate",value:function(){var e=h()(u.a.mark(function e(t){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("对方通话时长更新:",t.data),this.emitSyncEvent("peer_call_time_update",t.data);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"handleFeedUpdate",value:function(){var e=h()(u.a.mark(function e(t){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log("对方投喂精灵:",t.data),this.emitSyncEvent("peer_feed_update",t.data);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"emitSyncEvent",value:function(e,t){window.Vue&&window.Vue.prototype.$eventBus&&window.Vue.prototype.$eventBus.$emit(e,t),k.default.dispatch("sprite/handlePeerSyncEvent",{eventType:e,data:t})}},{key:"startPeriodicSync",value:function(){var e=this;this.syncInterval=setInterval(function(){e.sendHeartbeat()},3e4)}},{key:"sendHeartbeat",value:function(){var e=h()(u.a.mark(function e(){var t,n;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=k.default.getters["sprite/currentSprite"]){e.next=3;break}return e.abrupt("return");case 3:return n={timestamp:Date.now(),type:"heartbeat",spriteId:t.id,userId:this.getUserId(),data:{level:Math.floor(t.experience/50)+1,isOnline:!0}},e.next=6,this.sendSyncData(n);case 6:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"getUserId",value:function(){return"user_"+Date.now()}},{key:"delay",value:function(e){return new r.a(function(t){return setTimeout(t,e)})}},{key:"stop",value:function(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=null),this.isInitialized=!1,console.log("精灵同步管理器已停止")}},{key:"destroy",value:function(){this.stop(),this.syncQueue=[],this.plugins=null,console.log("精灵同步管理器已销毁")}}]),e}()),_=T,E=n("3xks"),x=(new(function(){function e(){m()(this,e),this.testResults=[],this.isTestMode=!1}return g()(e,[{key:"enableTestMode",value:function(){this.isTestMode=!0,console.log("测试模式已开启"),"undefined"!=typeof window&&(window.spriteTest=this)}},{key:"disableTestMode",value:function(){this.isTestMode=!1,console.log("测试模式已关闭"),"undefined"!=typeof window&&delete window.spriteTest}},{key:"logTestResult",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",s={testName:e,success:t,message:n,timestamp:(new Date).toISOString()};this.testResults.push(s);var a=t?"✅":"❌";return console.log(a+" "+e+": "+n),s}},{key:"getTestReport",value:function(){var e=this.testResults.length,t=this.testResults.filter(function(e){return e.success}).length;return{total:e,passed:t,failed:e-t,passRate:e>0?(t/e*100).toFixed(2)+"%":"0%",results:this.testResults}}},{key:"clearTestResults",value:function(){this.testResults=[],console.log("测试结果已清除")}},{key:"testSpriteClaim",value:function(){var e=h()(u.a.mark(function e(){var t,n,s;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("开始测试精灵认领功能..."),e.next=4,k.default.dispatch("sprite/clearAllSpriteData");case 4:return e.next=6,k.default.dispatch("sprite/claimSprite",1);case 6:(t=e.sent)&&t.id?(this.logTestResult("精灵认领",!0,"成功认领精灵: "+t.name),n=k.default.state.sprite.user.hasSprite,s=k.default.state.sprite.user.currentSpriteId,n&&s===t.id?this.logTestResult("精灵状态更新",!0,"用户状态正确更新"):this.logTestResult("精灵状态更新",!1,"用户状态更新失败")):this.logTestResult("精灵认领",!1,"认领精灵失败"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),this.logTestResult("精灵认领",!1,"认领失败: "+e.t0.message);case 13:case"end":return e.stop()}},e,this,[[0,10]])}));return function(){return e.apply(this,arguments)}}()},{key:"testFeedSprite",value:function(){var e=h()(u.a.mark(function e(){var t,n,s,a,r;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("开始测试投喂功能..."),t=k.default.getters["sprite/currentSprite"]){e.next=6;break}return this.logTestResult("投喂功能",!1,"没有当前精灵"),e.abrupt("return");case 6:return n=t.experience,k.default.commit("sprite/SET_LAST_FEED_DATE",null),e.next=10,k.default.dispatch("sprite/feedSprite");case 10:(s=e.sent).success?(a=k.default.getters["sprite/currentSprite"].experience,20===(r=a-n)?this.logTestResult("投喂功能",!0,"投喂成功，获得"+r+"经验值"):this.logTestResult("投喂功能",!1,"经验值增长异常: "+r)):this.logTestResult("投喂功能",!1,s.message),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(0),this.logTestResult("投喂功能",!1,"投喂失败: "+e.t0.message);case 17:case"end":return e.stop()}},e,this,[[0,14]])}));return function(){return e.apply(this,arguments)}}()},{key:"testCallDuration",value:function(){var e=h()(u.a.mark(function e(){var t,n,s,a=this;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("开始测试通话时长功能..."),t=k.default.getters["sprite/currentSprite"]){e.next=6;break}return this.logTestResult("通话时长",!1,"没有当前精灵"),e.abrupt("return");case 6:n=t.experience,s=k.default.state.sprite.user.totalCallTime,w.startTestCall(),this.logTestResult("开始通话",!0,"模拟通话开始"),setTimeout(h()(u.a.mark(function e(){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:w.endTestCall(),setTimeout(function(){var e=k.default.getters["sprite/currentSprite"].experience,t=k.default.state.sprite.user.totalCallTime;t>s?a.logTestResult("通话时长更新",!0,"通话时长增加: "+(t-s)+"分钟"):a.logTestResult("通话时长更新",!1,"通话时长未更新"),e>n?a.logTestResult("通话经验值",!0,"经验值增加: "+(e-n)):a.logTestResult("通话经验值",!1,"经验值未增加")},1e3);case 2:case"end":return e.stop()}},e,a)})),2e3),e.next=16;break;case 13:e.prev=13,e.t0=e.catch(0),this.logTestResult("通话时长",!1,"测试失败: "+e.t0.message);case 16:case"end":return e.stop()}},e,this,[[0,13]])}));return function(){return e.apply(this,arguments)}}()},{key:"testSpriteNameUpdate",value:function(){var e=h()(u.a.mark(function e(){var t,n,s;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,console.log("开始测试精灵名字修改..."),t=k.default.getters["sprite/currentSprite"]){e.next=6;break}return this.logTestResult("名字修改",!1,"没有当前精灵"),e.abrupt("return");case 6:return n=t.name,s="测试精灵"+Date.now(),e.next=10,k.default.dispatch("sprite/updateSpriteName",{spriteId:t.id,name:s});case 10:if(k.default.getters["sprite/currentSprite"].name!==s){e.next=17;break}return this.logTestResult("名字修改",!0,"名字成功修改为: "+s),e.next=15,k.default.dispatch("sprite/updateSpriteName",{spriteId:t.id,name:n});case 15:e.next=18;break;case 17:this.logTestResult("名字修改",!1,"名字修改失败");case 18:e.next=23;break;case 20:e.prev=20,e.t0=e.catch(0),this.logTestResult("名字修改",!1,"修改失败: "+e.t0.message);case 23:case"end":return e.stop()}},e,this,[[0,20]])}));return function(){return e.apply(this,arguments)}}()},{key:"testDataPersistence",value:function(){var e=h()(u.a.mark(function e(){return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("开始测试数据持久化..."),e.next=4,k.default.dispatch("sprite/saveSpriteData");case 4:if(!e.sent){e.next=12;break}return this.logTestResult("数据保存",!0,"数据保存成功"),e.next=9,k.default.dispatch("sprite/loadSpriteData");case 9:this.logTestResult("数据加载",!0,"数据加载成功"),e.next=13;break;case 12:this.logTestResult("数据保存",!1,"数据保存失败");case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(0),this.logTestResult("数据持久化",!1,"测试失败: "+e.t0.message);case 18:case"end":return e.stop()}},e,this,[[0,15]])}));return function(){return e.apply(this,arguments)}}()},{key:"testAnimations",value:function(){var e=this;try{console.log("开始测试动画效果...");var t=document.createElement("div");t.style.width="100px",t.style.height="100px",t.style.position="relative",t.style.background="#ccc",t.style.margin="20px",document.body.appendChild(t),setTimeout(function(){E.a.playFeedAnimation(t,!0),e.logTestResult("投喂动画",!0,"投喂动画播放成功")},500),setTimeout(function(){E.a.playTouchAnimation(t),e.logTestResult("抚摸动画",!0,"抚摸动画播放成功")},1500),setTimeout(function(){E.a.playLevelUpAnimation(t,5),e.logTestResult("升级动画",!0,"升级动画播放成功")},2500),setTimeout(function(){E.a.playGlowEffect(t),e.logTestResult("闪光特效",!0,"闪光特效播放成功")},3500),setTimeout(function(){document.body.removeChild(t)},5e3)}catch(e){this.logTestResult("动画效果",!1,"测试失败: "+e.message)}}},{key:"runAllTests",value:function(){var e=h()(u.a.mark(function e(){var t=this;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return console.log("开始运行所有测试..."),this.clearTestResults(),e.prev=2,e.next=5,this.testSpriteClaim();case 5:return e.next=7,this.testFeedSprite();case 7:return e.next=9,this.testSpriteNameUpdate();case 9:return e.next=11,this.testDataPersistence();case 11:this.testAnimations(),setTimeout(function(){t.testCallDuration()},1e3),setTimeout(function(){var e=t.getTestReport();console.log("测试报告:",e),"undefined"!=typeof window&&window.alert&&alert("测试完成！\n总计: "+e.total+"\n通过: "+e.passed+"\n失败: "+e.failed+"\n通过率: "+e.passRate)},8e3),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(2),console.error("测试运行失败:",e.t0);case 19:case"end":return e.stop()}},e,this,[[2,16]])}));return function(){return e.apply(this,arguments)}}()},{key:"performanceTest",value:function(){console.log("开始性能测试...");for(var e=performance.now(),t=0;t<1e3;t++)k.default.commit("sprite/UPDATE_CURRENT_CALL_DURATION",t);var n=performance.now()-e;this.logTestResult("性能测试",n<100,"1000次状态更新耗时: "+n.toFixed(2)+"ms")}}]),e}()),n("WF7N")),A=n.n(x),M=new(function(){function e(){m()(this,e),this.observers=new A.a,this.timers=new A.a,this.memoryUsage=[],this.isMonitoring=!1}return g()(e,[{key:"startMonitoring",value:function(){this.isMonitoring||(this.isMonitoring=!0,console.log("性能监控已开启"),this.startMemoryMonitoring(),this.startDOMMonitoring(),this.startLongTaskMonitoring())}},{key:"stopMonitoring",value:function(){this.isMonitoring=!1,this.observers.forEach(function(e){return e.disconnect()}),this.observers.clear(),this.timers.forEach(function(e){return clearInterval(e)}),this.timers.clear(),console.log("性能监控已停止")}},{key:"startMemoryMonitoring",value:function(){var e=this;if(performance.memory){var t=setInterval(function(){var t={used:performance.memory.usedJSHeapSize,total:performance.memory.totalJSHeapSize,limit:performance.memory.jsHeapSizeLimit,timestamp:Date.now()};e.memoryUsage.push(t),e.memoryUsage.length>100&&e.memoryUsage.shift(),e.checkMemoryLeak()},5e3);this.timers.set("memory",t)}else console.warn("浏览器不支持内存监控")}},{key:"checkMemoryLeak",value:function(){if(!(this.memoryUsage.length<10)){var e=this.memoryUsage.slice(-10),t=this.calculateMemoryTrend(e);t>1048576&&console.warn("检测到可能的内存泄漏，内存使用持续增长:",this.formatBytes(t))}}},{key:"calculateMemoryTrend",value:function(e){if(e.length<2)return 0;var t=e[0].used;return e[e.length-1].used-t}},{key:"formatBytes",value:function(e){if(0===e)return"0 Bytes";var t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}},{key:"startDOMMonitoring",value:function(){if(window.MutationObserver){var e=0,t=Date.now(),n=new MutationObserver(function(n){if((e+=n.length)%100==0){var s=Date.now()-t,a=e/(s/1e3);a>50&&console.warn("DOM变化频率过高: "+a.toFixed(2)+" 次/秒")}});n.observe(document.body,{childList:!0,subtree:!0,attributes:!0}),this.observers.set("dom",n)}else console.warn("浏览器不支持DOM监控")}},{key:"startLongTaskMonitoring",value:function(){if(window.PerformanceObserver)try{var e=new PerformanceObserver(function(e){e.getEntries().forEach(function(e){e.duration>50&&console.warn("检测到长任务: "+e.duration.toFixed(2)+"ms",e)})});e.observe({entryTypes:["longtask"]}),this.observers.set("longtask",e)}catch(e){console.warn("长任务监控初始化失败:",e)}else console.warn("浏览器不支持长任务监控")}},{key:"debounce",value:function(e,t){var n=void 0;return function(){for(var s=arguments.length,a=Array(s),r=0;r<s;r++)a[r]=arguments[r];clearTimeout(n),n=setTimeout(function(){clearTimeout(n),e.apply(void 0,a)},t)}}},{key:"throttle",value:function(e,t){var n=void 0;return function(){if(!n){for(var s=arguments.length,a=Array(s),r=0;r<s;r++)a[r]=arguments[r];e.apply(this,a),n=!0,setTimeout(function(){return n=!1},t)}}}},{key:"lazyLoadImages",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"img[data-src]";if(!window.IntersectionObserver)return console.warn("浏览器不支持IntersectionObserver，使用降级方案"),void this.fallbackLazyLoad(e);var t=document.querySelectorAll(e),n=new IntersectionObserver(function(e){e.forEach(function(e){if(e.isIntersecting){var t=e.target;t.src=t.dataset.src,t.removeAttribute("data-src"),n.unobserve(t)}})});t.forEach(function(e){return n.observe(e)}),this.observers.set("images",n)}},{key:"fallbackLazyLoad",value:function(e){var t=document.querySelectorAll(e),n=this.throttle(function(){t.forEach(function(e){if(e.dataset.src){var t=e.getBoundingClientRect();t.top<window.innerHeight&&t.bottom>0&&function(e){e.dataset.src&&(e.src=e.dataset.src,e.removeAttribute("data-src"))}(e)}})},100);window.addEventListener("scroll",n),window.addEventListener("resize",n),n()}},{key:"optimizeAnimations",value:function(){var e=document.createElement("div");e.style.transform="translateZ(0)",document.body.appendChild(e);var t="none"!==window.getComputedStyle(e).transform;document.body.removeChild(e),t||console.warn("设备可能不支持硬件加速，动画性能可能受影响"),document.querySelectorAll(".sprite-avatar, .particle, .effect-container").forEach(function(e){e.style.willChange="transform, opacity",e.style.transform="translateZ(0)"})}},{key:"cleanupEventListeners",value:function(){console.log("清理未使用的事件监听器")}},{key:"getPerformanceReport",value:function(){return{memoryUsage:this.memoryUsage.slice(-10),currentMemory:this.getCurrentMemoryUsage(),recommendations:this.getOptimizationRecommendations()}}},{key:"getCurrentMemoryUsage",value:function(){return performance.memory?{used:this.formatBytes(performance.memory.usedJSHeapSize),total:this.formatBytes(performance.memory.totalJSHeapSize),limit:this.formatBytes(performance.memory.jsHeapSizeLimit)}:null}},{key:"getOptimizationRecommendations",value:function(){var e=[];return performance.memory&&performance.memory.usedJSHeapSize>52428800&&e.push("内存使用较高，建议清理不必要的数据"),document.querySelectorAll("*").length>1e3&&e.push("DOM节点数量较多，建议优化DOM结构"),this.observers.size+this.timers.size>10&&e.push("事件监听器较多，建议清理不必要的监听器"),e}},{key:"applyOptimizations",value:function(){console.log("应用性能优化..."),this.lazyLoadImages(),this.optimizeAnimations(),this.cleanupEventListeners(),console.log("性能优化应用完成")}}]),e}()),P=M,R=new(function(){function e(){m()(this,e),this.errors=[],this.maxErrors=100,this.isInitialized=!1}return g()(e,[{key:"init",value:function(){var e=this;this.isInitialized||(window.addEventListener("error",function(t){e.handleError({type:"javascript",message:t.message,filename:t.filename,lineno:t.lineno,colno:t.colno,error:t.error,timestamp:Date.now()})}),window.addEventListener("unhandledrejection",function(t){e.handleError({type:"promise",message:t.reason&&t.reason.message||"Unhandled Promise Rejection",reason:t.reason,timestamp:Date.now()})}),window.Vue&&(window.Vue.config.errorHandler=function(t,n,s){e.handleError({type:"vue",message:t.message,error:t,component:n&&n.$options&&n.$options.name||"Unknown",info:s,timestamp:Date.now()})}),this.isInitialized=!0,console.log("错误处理系统已初始化"))}},{key:"handleError",value:function(e){this.errors.push(e),this.errors.length>this.maxErrors&&this.errors.shift(),console.error("捕获到错误:",e),this.processError(e)}},{key:"processError",value:function(e){switch(e.type){case"sprite":this.handleSpriteError(e);break;case"network":this.handleNetworkError(e);break;case"storage":this.handleStorageError(e);break;case"animation":this.handleAnimationError(e);break;default:this.handleGenericError(e)}}},{key:"handleSpriteError",value:function(e){console.warn("精灵系统错误:",e.message),e.recoverable&&this.attemptSpriteRecovery()}},{key:"handleNetworkError",value:function(e){console.warn("网络错误:",e.message),e.retryable&&this.scheduleRetry(e)}},{key:"handleStorageError",value:function(e){console.warn("存储错误:",e.message),this.tryFallbackStorage()}},{key:"handleAnimationError",value:function(e){console.warn("动画错误:",e.message),this.disableProblematicAnimations()}},{key:"handleGenericError",value:function(e){console.error("通用错误:",e.message)}},{key:"attemptSpriteRecovery",value:function(){var e=h()(u.a.mark(function e(){var t;return u.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,console.log("尝试恢复精灵状态..."),e.next=4,new Promise(function(e){e()}).then(n.bind(null,"IcnI"));case 4:return t=e.sent,e.next=7,t.default.dispatch("sprite/loadSpriteData");case 7:console.log("精灵状态恢复成功"),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),console.error("精灵状态恢复失败:",e.t0);case 13:case"end":return e.stop()}},e,this,[[0,10]])}));return function(){return e.apply(this,arguments)}}()},{key:"scheduleRetry",value:function(e){var t=Math.min(1e3*Math.pow(2,e.retryCount||0),3e4);setTimeout(function(){console.log("重试操作:",e.operation)},t)}},{key:"tryFallbackStorage",value:function(){console.log("尝试使用备用存储方案")}},{key:"disableProblematicAnimations",value:function(){console.log("禁用有问题的动画"),"undefined"!=typeof window&&(window.DISABLE_ANIMATIONS=!0)}},{key:"logError",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.handleError({type:e,message:t,details:n,timestamp:Date.now(),custom:!0})}},{key:"logWarning",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};console.warn("警告:",e,t)}},{key:"logInfo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};console.log("信息:",e,t)}},{key:"getErrorStats",value:function(){var e={total:this.errors.length,byType:{},recent:this.errors.slice(-10)};return this.errors.forEach(function(t){e.byType[t.type]=(e.byType[t.type]||0)+1}),e}},{key:"clearErrors",value:function(){this.errors=[],console.log("错误记录已清除")}},{key:"exportErrorLog",value:function(){var e={timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,url:window.location.href,errors:this.errors};return o()(e,null,2)}},{key:"checkSystemHealth",value:function(){var e={status:"healthy",issues:[],recommendations:[]};return this.errors.filter(function(e){return Date.now()-e.timestamp<6e4}).length>5&&(e.status="warning",e.issues.push("错误频率过高"),e.recommendations.push("检查系统稳定性")),performance.memory&&performance.memory.usedJSHeapSize>104857600&&(e.status="warning",e.issues.push("内存使用过高"),e.recommendations.push("清理内存或重启应用")),this.errors.filter(function(e){return"storage"===e.type}).length>0&&(e.status="warning",e.issues.push("存储系统异常"),e.recommendations.push("检查存储权限和空间")),e}}]),e}());"undefined"!=typeof window&&R.init();var L=new(function(){function e(){m()(this,e),this.feedbackQueue=[],this.userPreferences={},this.isInitialized=!1}return g()(e,[{key:"init",value:function(){this.isInitialized||(this.loadUserPreferences(),this.setupAccessibility(),this.setupResponsiveDesign(),this.setupUserFeedback(),this.isInitialized=!0,console.log("用户体验优化器已初始化"))}},{key:"loadUserPreferences",value:function(){try{var e=localStorage.getItem("sprite_user_preferences");this.userPreferences=e?JSON.parse(e):{animationsEnabled:!0,soundEnabled:!0,reducedMotion:!1,highContrast:!1,fontSize:"normal",theme:"default"}}catch(e){console.warn("加载用户偏好设置失败:",e),this.userPreferences={}}}},{key:"saveUserPreferences",value:function(){try{localStorage.setItem("sprite_user_preferences",o()(this.userPreferences))}catch(e){console.warn("保存用户偏好设置失败:",e)}}},{key:"setupAccessibility",value:function(){window.matchMedia("(prefers-reduced-motion: reduce)").matches&&(this.userPreferences.reducedMotion=!0,this.applyReducedMotion()),window.matchMedia("(prefers-contrast: high)").matches&&(this.userPreferences.highContrast=!0,this.applyHighContrast()),this.setupKeyboardNavigation(),this.setupScreenReaderSupport()}},{key:"applyReducedMotion",value:function(){var e=document.createElement("style");e.textContent="\n      *, *::before, *::after {\n        animation-duration: 0.01ms !important;\n        animation-iteration-count: 1 !important;\n        transition-duration: 0.01ms !important;\n      }\n    ",document.head.appendChild(e)}},{key:"applyHighContrast",value:function(){document.body.classList.add("high-contrast")}},{key:"setupKeyboardNavigation",value:function(){document.addEventListener("keydown",function(e){"Tab"===e.key&&document.body.classList.add("keyboard-navigation")}),document.addEventListener("mousedown",function(){document.body.classList.remove("keyboard-navigation")});var e=document.createElement("style");e.textContent="\n      .keyboard-navigation *:focus {\n        outline: 2px solid #409eff !important;\n        outline-offset: 2px !important;\n      }\n    ",document.head.appendChild(e)}},{key:"setupScreenReaderSupport",value:function(){var e=document.createElement("div");e.setAttribute("aria-live","polite"),e.setAttribute("aria-atomic","true"),e.style.position="absolute",e.style.left="-10000px",e.style.width="1px",e.style.height="1px",e.style.overflow="hidden",e.id="sprite-live-region",document.body.appendChild(e)}},{key:"announceToScreenReader",value:function(e){var t=document.getElementById("sprite-live-region");t&&(t.textContent=e)}},{key:"setupResponsiveDesign",value:function(){var e=this,t=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),n=/iPad|Android/i.test(navigator.userAgent)&&window.innerWidth>=768;t?(document.body.classList.add("mobile-device"),this.optimizeForMobile()):n?(document.body.classList.add("tablet-device"),this.optimizeForTablet()):document.body.classList.add("desktop-device"),window.addEventListener("orientationchange",function(){setTimeout(function(){e.handleOrientationChange()},100)})}},{key:"optimizeForMobile",value:function(){var e=document.createElement("style");e.textContent="\n      .mobile-device .el-button {\n        min-height: 44px !important;\n        min-width: 44px !important;\n      }\n      .mobile-device .interaction-btn {\n        height: 60px !important;\n      }\n    ",document.head.appendChild(e);var t=document.querySelector('meta[name="viewport"]');t&&t.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no")}},{key:"optimizeForTablet",value:function(){var e=document.createElement("style");e.textContent="\n      .tablet-device .sprite-container {\n        max-width: 600px;\n        margin: 0 auto;\n      }\n    ",document.head.appendChild(e)}},{key:"handleOrientationChange",value:function(){window.dispatchEvent(new Event("resize")),this.announceToScreenReader("屏幕方向已改变")}},{key:"setupUserFeedback",value:function(){this.trackUserInteractions(),this.monitorPerformance()}},{key:"trackUserInteractions",value:function(){var e=this,t=Date.now();document.addEventListener("click",function(){0,t=Date.now()}),document.addEventListener("touchstart",function(){0,t=Date.now()}),setInterval(function(){Date.now()-t>3e5&&e.handleUserInactivity()},6e4)}},{key:"handleUserInactivity",value:function(){console.log("用户长时间无操作，考虑优化性能"),window.spriteAnimationManager&&window.spriteAnimationManager.pauseAnimations()}},{key:"monitorPerformance",value:function(){var e=this;window.addEventListener("load",function(){setTimeout(function(){var t=performance.getEntriesByType("navigation")[0];t&&(t.loadEventEnd-t.loadEventStart>3e3&&e.provideFeedback("页面加载较慢，建议优化"))},1e3)}),performance.memory&&setInterval(function(){performance.memory.usedJSHeapSize/1024/1024>100&&e.provideFeedback("内存使用较高，建议刷新页面")},3e4)}},{key:"provideFeedback",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";this.feedbackQueue.push({message:e,type:t,timestamp:Date.now()}),window.Vue&&window.Vue.prototype.$message?window.Vue.prototype.$message({message:e,type:"info"===t?"info":t}):console.log("用户反馈 ["+t+"]: "+e)}},{key:"optimizeTouchExperience",value:function(){document.addEventListener("touchstart",function(e){var t=e.target.closest(".el-button, .interaction-btn");t&&t.classList.add("touch-active")}),document.addEventListener("touchend",function(e){var t=e.target.closest(".el-button, .interaction-btn");t&&setTimeout(function(){t.classList.remove("touch-active")},150)});var e=document.createElement("style");e.textContent="\n      .touch-active {\n        transform: scale(0.95) !important;\n        opacity: 0.8 !important;\n        transition: all 0.1s ease !important;\n      }\n    ",document.head.appendChild(e)}},{key:"getUXReport",value:function(){return{userPreferences:this.userPreferences,feedbackQueue:this.feedbackQueue.slice(-10),deviceInfo:{userAgent:navigator.userAgent,screenSize:window.screen.width+"x"+window.screen.height,viewportSize:window.innerWidth+"x"+window.innerHeight,pixelRatio:window.devicePixelRatio},accessibility:{reducedMotion:this.userPreferences.reducedMotion,highContrast:this.userPreferences.highContrast,keyboardNavigation:document.body.classList.contains("keyboard-navigation")}}}},{key:"applyUserPreferences",value:function(){this.userPreferences.animationsEnabled||this.applyReducedMotion(),this.userPreferences.highContrast&&this.applyHighContrast(),"normal"!==this.userPreferences.fontSize&&document.body.classList.add("font-size-"+this.userPreferences.fontSize),"default"!==this.userPreferences.theme&&document.body.classList.add("theme-"+this.userPreferences.theme)}}]),e}()),I=L,z={name:"App",data:function(){return{videoUrl:"",srcUrl:"",input:"",dir:"/",offset:0,size:20,inputDcLabel:"",description:"",record:"开始视频录屏",mediaRecorder:null,createdDcLabels:[],checkedDcLabel:"",file_text1:"",file_name1:"",file_text2:"",file_name2:"",msgList:[],clickResult:!1,imgList:[]}},created:function(){var e=this;return h()(u.a.mark(function t(){return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.registerCallback(),t.next=3,e.initializeSpriteSystem();case 3:case"end":return t.stop()}},t,e)}))()},computed:{dcLabels:function(){return this.inputDcLabel.split(",")}},methods:{createDC:function(){var e=this;this.$nextTick(function(){if(e.dcLabels&&e.description){var t=e.dcLabels,n=e.description;p.default.dcPlugin.createDC({dcLabels:t,description:n}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[createDC] then: "+o()(t)),e.showSuccessMessage("DC创建已调用")}).catch(function(t){console.log("jssdk-demo","[createDC] catch: "+t),e.showErrorMessage("DC创建失败:\n"+t)})}else e.$message({message:"请先上传相关文件",type:"error"})})},downloadFile:function(){var e=this;p.default.dcPlugin.downloadFile({dcLabel:"xxxxxx",uri:"/sdadfa/ss",headers:{name:"value"}}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[downloadFile] then: "+o()(t)),e.showSuccessMessage("下载DC文件:"+t.data.absolutePath)}).catch(function(t){console.log("jssdk-demo","[downloadFile] catch: "+t),e.showErrorMessage("下载DC文件:\n"+t)})},closeDC:function(){var e=this;0!==this.createdDcLabels.length?""!==this.checkedDcLabel?p.default.dcPlugin.closeDC({dcLabel:this.checkedDcLabel}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[closeDC] then: "+o()(t)),e.showSuccessMessage("DC关闭已调用")}).catch(function(t){console.log("jssdk-demo","[closeDC] catch: "+t),e.showErrorMessage("DC关闭失败:"+t)}):this.showErrorMessage("请选择dcLabel"):this.showErrorMessage("请先创建DC，或者等待创建ADC")},sendDCData:function(){var e=this;0!==this.createdDcLabels.length?""!==this.checkedDcLabel?""!==this.input?p.default.dcPlugin.sendData({dcLabel:this.checkedDcLabel,data:this.input}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[sendData] then: "+o()(t));var n={name:e.checkedDcLabel,message:e.input,isSend:!0,timeString:e.getCurrentDate()};e.msgList.push(n),e.showSuccessMessage(t.message)}).catch(function(t){console.log("jssdk-demo","[sendData] catch: "+t),e.showErrorMessage("DC数据发送失败:\n"+t)}):this.showErrorMessage("请输入发送内容"):this.showErrorMessage("请选择dcLabel"):this.showErrorMessage("请先创建DC，或者等待创建ADC")},queryDCMaxDataSize:function(){var e=this;0!==this.createdDcLabels.length?""!==this.checkedDcLabel?p.default.dcPlugin.queryDCMaxDataSize({dcLabel:this.checkedDcLabel}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[queryDCMaxDataSize] then: "+o()(t)),e.showSuccessMessage("DC最大数据包:"+t.data.maxmessagesize+" byte")}).catch(function(t){console.log("jssdk-demo","[queryDCMaxDataSize] catch: "+t),e.showErrorMessage("DC最大数据包查询失败:\n"+t)}):this.showErrorMessage("请选择dcLabel"):this.showErrorMessage("请先创建DC，或者等待创建ADC")},ifPeerSupportDC:function(){var e=this;p.default.dcPlugin.ifPeerSupportDC({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[ifPeerSupportDC] then: "+o()(t)),e.showSuccessMessage("对端是否支持DC:"+t.data.ifPeerSupportDC)}).catch(function(t){console.log("jssdk-demo","[ifPeerSupportDC] catch: "+t),e.showErrorMessage("对端是否支持DC:\n"+t)})},getMediaFolderList:function(){var e=this;p.default.mediaPlugin.getMediaFolderList({}).then(function(t){if(1!==t.code)return r.a.reject(t.data.message);console.log("jssdk-demo","[getMediaFolderList] then: "+o()(t)),e.showSuccessMessage("获取多媒体文件夹列表成功")}).catch(function(t){console.log("jssdk-demo","[getMediaFolderList] catch: "+t),e.showErrorMessage("获取多媒体文件夹列表失败:\n"+t)})},queryImages:function(){var e=this;p.default.mediaPlugin.queryImages({path:this.dir,offset:this.offset,size:this.size}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[queryImages] then: "+o()(t)),e.imgList=t.data.value,console.log(e.queryImageSrc),e.showSuccessMessage("查询图片列表成功")}).catch(function(t){console.log("jssdk-demo","[queryImages] catch: "+t),e.showErrorMessage("查询图片列表失败:\n"+t)})},queryVideos:function(){var e=this;p.default.mediaPlugin.queryVideos({path:this.dir,offset:this.offset,size:this.size}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[queryVideos] then: "+o()(t)),e.showSuccessMessage("查询视频列表成功")}).catch(function(t){console.log("jssdk-demo","[queryVideos] catch: "+t),e.showErrorMessage("查询视频列表失败:\n"+t)})},queryFiles:function(){var e=this;p.default.mediaPlugin.queryFiles({path:this.dir,offset:this.offset,size:this.size}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[queryFiles] then: "+o()(t)),e.showSuccessMessage("查询文件列表成功")}).catch(function(t){console.log("jssdk-demo","[queryFiles] catch: "+t),e.showErrorMessage("查询文件列表失败:\n"+t)})},checkFiles:function(){var e=this;p.default.mediaPlugin.checkFiles({path:["storage/0/Download","storage/0/Download/2017-05-17_17-33-30.mp4"]}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[checkFiles] then: "+o()(t)),e.showSuccessMessage("检查文件或文件夹路径成功")}).catch(function(t){console.log("jssdk-demo","[checkFiles] catch: "+t),e.showErrorMessage("检查文件或文件夹路径失败:\n"+t)})},saveFile:function(){var e=this;p.default.mediaPlugin.saveFile({file:(new TextEncoder).encode("xxxxxx").buffer,filePathType:0,filePath:"/photo/xxxxx.jpg"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[checkFiles] then: "+o()(t)),e.showSuccessMessage("文件保存成功")}).catch(function(t){console.log("jssdk-demo","[checkFiles] catch: "+t),e.showErrorMessage("文件保存失败:\n"+t)})},saveKeyValueData:function(){var e=this;p.default.mediaPlugin.saveKeyValueData({key:"xxx",value:"yyy"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[saveKeyValueData] then: "+o()(t)),e.showSuccessMessage("保存KV键值对成功")}).catch(function(t){console.log("jssdk-demo","[saveKeyValueData] catch: "+t),e.showErrorMessage("保存KV键值对失败:\n"+t)})},getKeyValueData:function(){var e=this;p.default.mediaPlugin.getKeyValueData({key:"xxx"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getKeyValueData] then: "+o()(t)),e.showSuccessMessage("获取KV键值对成功:"+t.data.value)}).catch(function(t){console.log("jssdk-demo","[getKeyValueData] catch: "+t),e.showErrorMessage("获取KV键值对失败:\n"+t)})},dealCompressedFile:function(){var e=this;p.default.mediaPlugin.dealCompressedFile({compressedFilePath:"/sdcard/file/xxx.zip",filePathType:0,filePath:"/sdcard/file",type:"zip"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[dealCompressedFile] then: "+o()(t)),e.showSuccessMessage("解压压缩包成功")}).catch(function(t){console.log("jssdk-demo","[dealCompressedFile] catch: "+t),e.showErrorMessage("解压压缩包失败:\n"+t)})},getLocation:function(){var e=this;p.default.mediaPlugin.getLocation({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getLocation] then: "+o()(t)),e.showSuccessMessage("获取位置信息成功")}).catch(function(t){console.log("jssdk-demo","[getLocation] catch: "+t),e.showErrorMessage("获取位置信息失败:\n"+t)})},getFileByPath:function(){var e=this;p.default.mediaPlugin.getFileByPath({path:"xxx"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getFileByPath] then: "+o()(t)),e.showSuccessMessage("根据文件path获取文件内容 成功")}).catch(function(t){console.log("jssdk-demo","[getFileByPath] catch: "+t),e.showErrorMessage("根据文件path获取文件内容 失败:\n"+t)})},getMiniAppInfo:function(){var e=this;p.default.bizPlugin.getMiniAppInfo({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getMiniAppInfo] then: "+o()(t)),e.showSuccessMessage("获取小程序配置信息成功")}).catch(function(t){console.log("jssdk-demo","[getMiniAppInfo] catch: "+t),e.showErrorMessage("获取小程序配置信息失败:\n"+t)})},openShareDataPage:function(){var e=this;p.default.bizPlugin.openApp({jumpType:"shareData",extras:"分享的内容"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[openShareDataPage] then: "+o()(t)),e.showSuccessMessage("启动分享界面成功")}).catch(function(t){console.log("jssdk-demo","[openShareDataPage] catch: "+t),e.showErrorMessage("启动分享主界面失败:\n"+t)})},openShareScreenPage:function(){var e=this;p.default.bizPlugin.openApp({jumpType:"shareScreen",extras:""}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[openShareScreenPage] then: "+o()(t)),e.showSuccessMessage("启动屏幕共享界面成功")}).catch(function(t){console.log("jssdk-demo","[openShareScreenPage] catch: "+t),e.showErrorMessage("启动屏幕共享界主界面失败:\n"+t)})},openAppScreenPage:function(){var e=this;p.default.bizPlugin.openApp({jumpType:"openOtherApp",extras:"https://h5.nf.migu.cn/app/v4/zt/2023/memone/index.html?cfrom=yyzs2"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[openAppScreenPage] then: "+o()(t)),e.showSuccessMessage("启动系统浏览器APP成功")}).catch(function(t){console.log("jssdk-demo","[openAppScreenPage] catch: "+t),e.showErrorMessage("启动系统浏览器APP失败:\n"+t)})},takePhoto:function(){var e=this;p.default.bizPlugin.openApp({jumpType:"openCameraToPic",extras:""}).then(function(t){if(1!==t.code)return r.a.reject(t.message);e.srcUrl=t.data.path,console.log("jssdk-demo","[takePhoto] then: "+o()(t)),e.showSuccessMessage("拍照成功")}).catch(function(t){console.log("jssdk-demo","[takePhoto] catch: "+t),e.showErrorMessage("拍照失败:\n"+t)})},takeVideo:function(){var e=this;p.default.bizPlugin.openApp({jumpType:"openCameraToVideo",extras:""}).then(function(t){if(1!==t.code)return r.a.reject(t.message);e.videoUrl=t.data.path,console.log("jssdk-demo","[takeVideo] then: "+o()(t)),e.showSuccessMessage("拍摄视频成功")}).catch(function(t){console.log("jssdk-demo","[takeVideo] catch: "+t),e.showErrorMessage("拍摄视频失败:\n"+t)})},screenshotVideo:function(){var e=this,t=this.$refs.videoPlayer,n=this.$refs.canvasRef,s=t.clientWidth;n.width=s;var a=t.clientHeight;n.height=a,n.getContext("2d").drawImage(t,0,0,s,a);var i=n.toDataURL("image/png");p.default.mediaPlugin.saveImageToAlbum({file:i}).then(function(t){if(1!==t.code)return r.a.reject(t.message);e.showSuccessMessage("视频截图保存成功，请在相册中查看！")}).catch(function(t){e.showSuccessMessage("视频截图保存失败：\n"+t)})},handleLoadedMeta:function(){var e=this,t=this.$refs.videoPlayer,n=[];this.mediaRecorder=new MediaRecorder(t.captureStream(25)),this.mediaRecorder.ondataavailable=function(e){n.push(e.data)},this.mediaRecorder.onstop=function(){var t=new Blob(n,{type:"video/mp4"}),s=new FileReader;s.readAsDataURL(t),s.onload=function(){var t=s.result;console.log("jssdk-demo","videoData: "+t),p.default.mediaPlugin.saveVideoToAlbum({file:t}).then(function(t){if(1!==t.code)return r.a.reject(t.message);e.showSuccessMessage("视频录屏保存成功，请在相册中查看！")}).catch(function(t){e.showSuccessMessage("视频录屏保存失败：\n"+t)})}}},recordVideo:function(){"开始视频录屏"===this.record?(this.record="结束视频录屏",this.mediaRecorder.start()):(this.record="开始视频录屏",this.mediaRecorder.stop())},setWindow:function(){var e=this;p.default.bizPlugin.setWindow({isWindowFront:!0,height:"1000",width:"1000",windowTop:"50",windowStart:"50",statusBarColor:"#00000000",navigationBarColor:"#00FFFFFF",transparency:"0",barEnable:!1}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[setWindow] then: "+o()(t)),e.showSuccessMessage("改变小程序窗口式样成功")}).catch(function(t){console.log("jssdk-demo","[setWindow] catch: "+t),e.showErrorMessage("改变小程序窗口式样失败:\n"+t)})},getContactList:function(){var e=this;p.default.bizPlugin.getContactList({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getContactList] then: "+o()(t)),e.showSuccessMessage("获取联系人成功")}).catch(function(t){console.log("jssdk-demo","[getContactList] catch: "+t),e.showErrorMessage("获取联系人失败:\n"+t)})},controlAR:function(){var e=this;p.default.bizPlugin.controlAR({status:0}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[controlAR] then: "+o()(t)),e.showSuccessMessage("AR开启或关闭成功")}).catch(function(t){console.log("jssdk-demo","[controlAR] catch: "+t),e.showErrorMessage("AR开启或关闭失败:\n"+t)})},isARAvailable:function(){var e=this;p.default.bizPlugin.isARAvailable({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[isARAvailable] then: "+o()(t)),e.showSuccessMessage("检查AR功能是否可用:"+(t.isARAvailable?"是":"否"))}).catch(function(t){console.log("jssdk-demo","[isARAvailable] catch: "+t),e.showErrorMessage("检查AR功能是否可用:\n"+t)})},enableDrawPoint:function(){var e=this;p.default.bizPlugin.enableDrawPoint({enable:!0}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[enableDrawPoint] then: "+o()(t)),e.showSuccessMessage("启用或禁用涂鸦功能成功")}).catch(function(t){console.log("jssdk-demo","[enableDrawPoint] catch: "+t),e.showErrorMessage("启用或禁用涂鸦功能:\n"+t)})},resetLastDrawPoint:function(){var e=this;p.default.bizPlugin.resetLastDrawPoint({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[resetLastDrawPoint] then: "+o()(t)),e.showSuccessMessage("撤销最后一个涂鸦成功")}).catch(function(t){console.log("jssdk-demo","[resetLastDrawPoint] catch: "+t),e.showErrorMessage("撤销最后一个涂鸦:\n"+t)})},resetAllDrawPoint:function(){var e=this;p.default.bizPlugin.resetAllDrawPoint({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[resetAllDrawPoint] then: "+o()(t)),e.showSuccessMessage("清除所有涂鸦成功")}).catch(function(t){console.log("jssdk-demo","[resetAllDrawPoint] catch: "+t),e.showErrorMessage("清除所有涂鸦:\n"+t)})},enableSticker:function(){var e=this;p.default.bizPlugin.enableSticker({enable:!0}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[enableSticker] then: "+o()(t)),e.showSuccessMessage("启用/禁用2d贴纸编辑成功")}).catch(function(t){console.log("jssdk-demo","[enableSticker] catch: "+t),e.showErrorMessage("启用/禁用2d贴纸编辑:\n"+t)})},addEditableSticker:function(){var e=this;p.default.bizPlugin.addEditableSticker({path:"xxx/xxx.plist"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[addEditableSticker] then: "+o()(t)),e.showSuccessMessage("添加2d可编辑贴纸成功")}).catch(function(t){console.log("jssdk-demo","[addEditableSticker] catch: "+t),e.showErrorMessage("添加2d可编辑贴纸:\n"+t)})},clearEditableSticker:function(){var e=this;p.default.bizPlugin.clearEditableSticker({path:"xxx/xxx.plist"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[clearEditableSticker] then: "+o()(t)),e.showSuccessMessage("清空2d可编辑贴纸")}).catch(function(t){console.log("jssdk-demo","[clearEditableSticker] catch: "+t),e.showErrorMessage("清空2d可编辑贴纸:\n"+t)})},setDrawPointColor:function(){var e=this;p.default.bizPlugin.setDrawPointColor({color:"#FFFFFF00"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[setDrawPointColor] then: "+o()(t)),e.showSuccessMessage("设置涂鸦颜色成功")}).catch(function(t){console.log("jssdk-demo","[setDrawPointColor] catch: "+t),e.showErrorMessage("设置涂鸦颜色:\n"+t)})},setDrawPointSize:function(){var e=this;p.default.bizPlugin.setDrawPointSize({size:10}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[setDrawPointSize] then: "+o()(t)),e.showSuccessMessage("设置涂鸦笔触大小成功")}).catch(function(t){console.log("jssdk-demo","[setDrawPointSize] catch: "+t),e.showErrorMessage("设置涂鸦笔触大小:\n"+t)})},loadARResource:function(){var e=this;p.default.bizPlugin.loadARResource({resource:""}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[loadARResource] then: "+o()(t)),e.showSuccessMessage("数字人渲染成功")}).catch(function(t){console.log("jssdk-demo","[loadARResource] catch: "+t),e.showErrorMessage("数字人渲染:\n"+t)})},setMiniAppHeight:function(){var e=this;p.default.bizPlugin.setMiniAppHeight({height:960}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[setMiniAppHeight] then: "+o()(t)),e.showSuccessMessage("设置⼩程序容器的⾼度成功")}).catch(function(t){console.log("jssdk-demo","[setMiniAppHeight] catch: "+t),e.showErrorMessage("设置⼩程序容器的⾼度:\n"+t)})},isARCameraFront:function(){var e=this;p.default.bizPlugin.isARCameraFront().then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[isARCameraFront] then: "+o()(t)),e.showSuccessMessage("当前是否使用前置摄像头:"+t.data.isARCameraFront)}).catch(function(t){console.log("jssdk-demo","[isARCameraFront] catch: "+t),e.showErrorMessage("当前是否使用前置摄像头:\n"+t)})},switchToFrontCamera:function(){var e=this;p.default.bizPlugin.switchToFrontCamera().then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[switchToFrontCamera] then: "+o()(t)),e.showSuccessMessage("切换至前置摄像头:"+t.data.switchToFrontCamera)}).catch(function(t){console.log("jssdk-demo","[switchToFrontCamera] catch: "+t),e.showErrorMessage("切换至前置摄像头:\n"+t)})},checkGbaEntitlement:function(){var e=this;p.default.bizPlugin.checkGbaEntitlement({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[checkGbaEntitlement] then: "+o()(t));var n="";switch(t.result){case"1000":n="鉴权成功";break;case"1001":n="用户不存在";break;case"1002":n="未授权";break;case"1003":n="系统错误"}e.showSuccessMessage("GBA接口"+n)}).catch(function(t){console.log("jssdk-demo","[checkGbaEntitlement] catch: "+t),e.showErrorMessage("GBA接口:\n"+t)})},closeWindow:function(){var e=this;p.default.bizPlugin.closeWindow({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[closeWindow] then: "+o()(t)),e.showSuccessMessage("关闭当前小程序页面成功")}).catch(function(t){console.log("jssdk-demo","[closeWindow] catch: "+t),e.showErrorMessage("关闭当前小程序页面失败:\n"+t)})},windowToBack:function(){var e=this;p.default.bizPlugin.windowToBack({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[windowToBack] then: "+o()(t)),e.showSuccessMessage("小程序页面退到后台成功")}).catch(function(t){console.log("jssdk-demo","[windowToBack] catch: "+t),e.showErrorMessage("小程序页面退到后台失败:\n"+t)})},windowToForeground:function(){var e=this;p.default.bizPlugin.windowToForeground({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[windowToForeground] then: "+o()(t)),e.showSuccessMessage("小程序页面恢复到前台成功")}).catch(function(t){console.log("jssdk-demo","[windowToForeground] catch: "+t),e.showErrorMessage("小程序页面恢复到前台失败:\n"+t)})},showAnimation:function(){var e=this;p.default.bizPlugin.showAnimation({resources:"xxx",showtime:30}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[showAnimation] then: "+o()(t)),e.showSuccessMessage("显示全屏动画成功")}).catch(function(t){console.log("jssdk-demo","[showAnimation] catch: "+t),e.showErrorMessage("显示全屏动画失败:\n"+t)})},getVersions:function(){var e=this;p.default.bizPlugin.getVersions({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getVersions] then: "+o()(t)),e.showSuccessMessage("获取当前OS与新通话SDK的版本成功")}).catch(function(t){console.log("jssdk-demo","[getVersions] catch: "+t),e.showErrorMessage("获取当前OS与新通话SDK的版本失败:\n"+t)})},callSubtitleFunc:function(){var e=this;p.default.bizPlugin.callSubtitleFunc({func:"showSubtitles",type:0,text:"您好，今天的天气怎么样？",backgroundColor:"#00000033",textColor:"#FFFFFF",height:408,textSize:57}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[callSubtitleFunc] then: "+o()(t)),e.showSuccessMessage("显示字幕成功")}).catch(function(t){console.log("jssdk-demo","[callSubtitleFunc] catch: "+t),e.showErrorMessage("显示字幕失败:\n"+t)})},callSubtitleFuncAdd:function(){var e=this;p.default.bizPlugin.callSubtitleFunc({func:"addSubtitles",text:"还行，就是感觉太热了！"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[callSubtitleFunc] then: "+o()(t)),e.showSuccessMessage("更新字幕成功")}).catch(function(t){console.log("jssdk-demo","[callSubtitleFunc] catch: "+t),e.showErrorMessage("更新字幕失败:\n"+t)})},callSubtitleFuncHide:function(){var e=this;p.default.bizPlugin.callSubtitleFunc({func:"hideSubtitles"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[callSubtitleFunc] then: "+o()(t)),e.showSuccessMessage("隐藏字幕成功")}).catch(function(t){console.log("jssdk-demo","[callSubtitleFunc] catch: "+t),e.showErrorMessage("隐藏字幕失败:\n"+t)})},getControlBarStyle:function(){var e=this;p.default.bizPlugin.getControlBarStyle({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[getControlBarStyle] then: "+o()(t)),e.showSuccessMessage("获取悬浮控制胶囊按钮信息成功")}).catch(function(t){console.log("jssdk-demo","[getControlBarStyle] catch: "+t),e.showErrorMessage("获取悬浮控制胶囊按钮信息失败:\n"+t)})},setControlBarStyle:function(){var e=this;p.default.bizPlugin.setControlBarStyle({gravity:1,isShowControlBar:!0,top:200,bottom:200,start:200,end:200}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[setControlBarStyle] then: "+o()(t)),e.showSuccessMessage("设置悬浮控制胶囊按钮成功")}).catch(function(t){console.log("jssdk-demo","[setControlBarStyle] catch: "+t),e.showErrorMessage("设置悬浮控制胶囊按钮失败:\n"+t)})},callVoiceHelperFuncSave:function(){var e=this;p.default.bizPlugin.callVoiceHelperFunc({func:"saveVoiceText",text:"xxx",pos:0}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[callVoiceHelperFunc] then: "+o()(t)),e.showSuccessMessage("语音助手控制成功")}).catch(function(t){console.log("jssdk-demo","[callVoiceHelperFunc] catch: "+t),e.showErrorMessage("语音助手控制失败:\n"+t)})},callVoiceHelperFuncClose:function(){var e=this;p.default.bizPlugin.callVoiceHelperFunc({func:"onVoiceHelperClosed"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[callVoiceHelperFunc] then: "+o()(t)),e.showSuccessMessage("语音助手控制成功")}).catch(function(t){console.log("jssdk-demo","[callVoiceHelperFunc] catch: "+t),e.showErrorMessage("语音助手控制失败:\n"+t)})},openRemoteApp:function(){var e=this;p.default.bizPlugin.openRemoteApp({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[openRemoteApp] then: "+o()(t)),e.showSuccessMessage("开启通话远端小程序成功")}).catch(function(t){console.log("jssdk-demo","[openRemoteApp] catch: "+t),e.showErrorMessage("开启通话远端小程序失败:\n"+t)})},closeRemoteApp:function(){var e=this;p.default.bizPlugin.closeRemoteApp({}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[closeRemoteApp] then: "+o()(t)),e.showSuccessMessage("关闭通话远端小程成功")}).catch(function(t){console.log("jssdk-demo","[closeRemoteApp] catch: "+t),e.showErrorMessage("关闭通话远端小程失败:\n"+t)})},callARFunc:function(){var e=this;p.default.bizPlugin.callARFunc({op:"graffitiColor",R:"100",G:"100",B:"100",size:"100",mediaId:"100"}).then(function(t){if(1!==t.code)return r.a.reject(t.message);console.log("jssdk-demo","[callAR] then: "+o()(t)),e.showSuccessMessage("与AR进行原生效果交互成功")}).catch(function(t){console.log("jssdk-demo","[callAR] catch: "+t),e.showErrorMessage("与AR进行原生效果交互失败:\n"+t)})},initializeSpriteSystem:function(){var e=this;return h()(u.a.mark(function t(){return u.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,S.default.init(p.default),t.next=4,w.init(p.default);case 4:return t.next=6,_.init(p.default);case 6:0,P.applyOptimizations(),I.init(),window.plugins=p.default,e.plugins=p.default,console.log("精灵系统初始化完成"),t.next=18;break;case 15:t.prev=15,t.t0=t.catch(0),console.error("精灵系统初始化失败:",t.t0);case 18:case"end":return t.stop()}},t,e,[[0,15]])}))()},registerCallback:function(){var e=this;p.default.callbackPlugin.dcNotified(function(t,n,s,a){if(console.log("jssdk-demo","[dcNotified] createdDcLabels: "+o()(e.createdDcLabels)),console.log("jssdk-demo","[dcNotified] params: "+o()(t)),1===t.imsDCStatus&&e.createdDcLabels.indexOf(t.dcLabel)<0&&e.createdDcLabels.push(t.dcLabel),3===t.imsDCStatus){var r=e.createdDcLabels,i=r.indexOf(t.dcLabel);i>=0&&(r.splice(i,1),e.createdDcLabels=r)}e.showSuccessMessage("DC状态变化："+t.imsDCStatus),n()}),p.default.callbackPlugin.messageNotified(function(t,n,s,a){console.log("jssdk-demo","[messageNotified] params: "+o()(t));var r=t.dcLabel,i=t.message,c=new TextDecoder("utf-8").decode(i);console.log("jssdk-demo","[messageNotified] message: "+c),"sprite_sync"===r?_.handleReceivedSyncMessage(c):e.showSuccessMessage("收到消息:"+r+" | "+c),n();var l={name:r,message:c,isSend:!1,timeString:e.getCurrentDate()};e.msgList.push(l)}),p.default.callbackPlugin.callStateNotified(function(t,n,s,a){console.log("jssdk-demo","[callStateNotified] params: "+o()(t)),e.showSuccessMessage("通话状态变化："+t.callInfo.callState),n()}),p.default.callbackPlugin.onLifecycleChanged(function(e,t,n,s){console.log("jssdk-demo","[onLifecycleChanged] params: "+o()(e)),t()}),p.default.callbackPlugin.arNotified(function(t,n,s,a){console.log("jssdk-demo","[arNotified] params: "+o()(t)),e.showSuccessMessage("收到AR SDK发来等消息"),n()})},showSuccessMessage:function(e){this.$message({message:e,type:"success"})},showErrorMessage:function(e){this.$message({message:e,type:"error"})},clickFile:function(e){var t=document.getElementById(e);t&&t.click()},fileInfo:function(e,t){var n=this,s=e.target.files[0];if(s){switch(!0){case"file_button1"===t:this.file_name1=s.name;break;case"file_button2"===t:this.file_name2=s.name}var a=new FileReader;a.readAsText(s,"UTF-8"),a.onload=function(e){var s=e.target.result;switch(!0){case"file_button1"===t:n.inputDcLabel=s;break;case"file_button2"===t:n.description=s}}}},readMsgList:function(){this.clickResult=!this.clickResult},getCurrentDate:function(){var e=new Date,t=e.getFullYear(),n=e.getMonth()+1;n=n<10?"0"+n:n;var s=e.getDate();s=s<10?"0"+s:s;var a=e.getHours();a=a<10?"0"+a:a;var r=e.getMinutes();r=r<10?"0"+r:r;var i=e.getSeconds();return t+":"+n+":"+s+" "+a+":"+r+":"+(i=i<10?"0"+i:i)},getImgBase64:function(e){console.log("id 1542",e),l;var t=document.getElementById(e),n=new XMLHttpRequest;n.open("GET",t.src,!0),n.responseType="blob",n.onload=function(){if(200===n.status){var e=n.response,t=new FileReader;console.log("reader 11",t),t.onloadend=function(){var e=t.result;console.log("Image data111:",e)},t.readAsDataURL(e)}},n.send()}}},j={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"app"}},[e.$route.path.startsWith("/sprite")?n("router-view"):n("div",{staticClass:"plugin"},[n("label",{staticClass:"label"},[e._v("DC插件")]),e._v(" "),n("div",{staticClass:"input_box"},[n("label",{staticClass:"font-12"},[e._v("上传DC标签配置文件:"),n("input",{staticStyle:{display:"none"},attrs:{type:"file",id:"file_button1"},on:{change:function(t){return e.fileInfo(t,"file_button1")}}})]),e._v(" "),n("el-button",{attrs:{size:"small"},on:{click:function(t){return e.clickFile("file_button1")}}},[e._v("选择DC标签配置文件")]),e._v(" "),""!==e.inputDcLabel?[n("p",{staticClass:"file_route"},[e._v("文件路径："+e._s(e.file_name1))]),e._v(" "),n("el-input",{staticClass:"m-bottom-16",attrs:{size:"small",type:"textarea",rows:2},model:{value:e.inputDcLabel,callback:function(t){e.inputDcLabel=t},expression:"inputDcLabel"}})]:e._e()],2),e._v(" "),n("div",{staticClass:"input_box m-top-16"},[n("label",{staticClass:"font-12"},[e._v("上传DC描述配置文件:"),n("input",{staticStyle:{display:"none"},attrs:{type:"file",id:"file_button2"},on:{change:function(t){return e.fileInfo(t,"file_button2")}}})]),e._v(" "),n("el-button",{attrs:{size:"small"},on:{click:function(t){return e.clickFile("file_button2")}}},[e._v("选择DC描述配置文件")]),e._v(" "),""!==e.description?[n("p",{staticClass:"file_route"},[e._v("文件路径："+e._s(e.file_name2))]),e._v(" "),n("el-input",{staticClass:"m-bottom-16",attrs:{size:"small",type:"textarea",rows:22},model:{value:e.description,callback:function(t){e.description=t},expression:"description"}})]:e._e()],2),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.createDC}},[e._v("创建DC")]),e._v(" "),e._l(e.createdDcLabels,function(t,s){return n("el-radio",{key:s,staticClass:"m-top-16",attrs:{label:t},model:{value:e.checkedDcLabel,callback:function(t){e.checkedDcLabel=t},expression:"checkedDcLabel"}},[e._v("\n      "+e._s(t)+"\n    ")])}),e._v(" "),n("el-input",{staticClass:"m-top-16",attrs:{id:"input",placeholder:"请输入发送内容",size:"small",type:"textarea",rows:2},model:{value:e.input,callback:function(t){e.input=t},expression:"input"}}),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.sendDCData}},[e._v("发送DC数据")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.queryDCMaxDataSize}},[e._v("查询DC通道数据包最大值")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.ifPeerSupportDC}},[e._v("查询对端是否支持DC")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.downloadFile}},[e._v("下载DC文件")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.closeDC}},[e._v("关闭DC")])],2),e._v(" "),n("div",{staticClass:"plugin"},[n("label",{staticClass:"label"},[e._v("媒体插件")]),e._v(" "),n("span",{attrs:{id:"media-input-span"}},[n("el-input",{attrs:{id:"media-input",placeholder:"目录",size:"small"},model:{value:e.dir,callback:function(t){e.dir=t},expression:"dir"}}),e._v(" "),n("el-input",{staticClass:"m-left-16",attrs:{id:"media-input2",placeholder:"起始位置",size:"small",oninput:"value=value.replace(/[^0-9.]/g,'')"},model:{value:e.offset,callback:function(t){e.offset=t},expression:"offset"}}),e._v(" "),n("el-input",{staticClass:"m-left-16",attrs:{id:"media-input3",placeholder:"数量",size:"small",oninput:"value=value.replace(/[^0-9.]/g,'')"},model:{value:e.size,callback:function(t){e.size=t},expression:"size"}})],1),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getMediaFolderList}},[e._v("获取多媒体文件夹列表")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.queryImages}},[e._v("获取图片列表")]),e._v(" "),e.imgList.length>0?n("div",{staticClass:"imgList"},e._l(e.imgList,function(t,s){return n("img",{key:s,attrs:{src:"file://"+t.filePath,id:"img_"+s,alt:""},on:{click:function(t){return e.getImgBase64("img_"+s)}}})}),0):e._e(),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.queryVideos}},[e._v("获取视频列表")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.checkFiles}},[e._v("检查文件或文件夹路径是否存在")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.queryFiles}},[e._v("获取文件列表")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.saveFile}},[e._v("保存文件")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.saveKeyValueData}},[e._v("保存KV键值对")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getKeyValueData}},[e._v("获取KV键值对")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.dealCompressedFile}},[e._v("解压压缩包")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getLocation}},[e._v("获取位置信息")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getFileByPath}},[e._v("根据文件path获取文件内容")])],1),e._v(" "),n("div",{staticClass:"plugin"},[n("label",{staticClass:"label"},[e._v("业务插件")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getMiniAppInfo}},[e._v("获取小程序配置信息\n    ")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.openShareDataPage}},[e._v("启动分享主界面\n    ")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.openShareScreenPage}},[e._v("启动屏幕共享界面\n    ")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.openAppScreenPage}},[e._v("启动系统浏览器APP\n    ")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.takePhoto}},[e._v("拍摄照片")]),e._v(" "),n("el-image",{staticClass:"img-style m-top-16",attrs:{src:e.srcUrl,fit:"cover"}}),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.takeVideo}},[e._v("拍摄视频")]),e._v(" "),n("video",{ref:"videoPlayer",staticClass:"video-style m-top-16",attrs:{src:e.videoUrl,controls:"controls",autoplay:"autoplay",type:"video/mp4",playsinline:"",preload:"metadata",crossorigin:"anonymous"},on:{loadedmetadata:e.handleLoadedMeta}}),e._v(" "),n("canvas",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"canvasRef"}),e._v(" "),n("div",{staticClass:"create-close m-top-16"},[n("el-button",{staticClass:"btn-1",attrs:{size:"small"},on:{click:e.screenshotVideo}},[e._v("\n        视频截屏\n      ")]),e._v(" "),n("el-button",{staticClass:"btn-1",attrs:{size:"small"},on:{click:e.recordVideo}},[e._v("\n        "+e._s(this.record)+"\n      ")])],1),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.setWindow}},[e._v("改变小程序窗口式样")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getContactList}},[e._v("获取联系人")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.controlAR}},[e._v("开启和关闭AR功能")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.isARAvailable}},[e._v("检查AR功能是否可用")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.enableDrawPoint}},[e._v("启用或禁用涂鸦功能")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.resetLastDrawPoint}},[e._v("撤销最后一个涂鸦")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.resetAllDrawPoint}},[e._v("清除所有涂鸦")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.enableSticker}},[e._v("启用/禁用2d贴纸编辑")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.addEditableSticker}},[e._v("添加2d可编辑贴纸")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.clearEditableSticker}},[e._v("清空2d可编辑贴纸")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.setDrawPointColor}},[e._v("设置涂鸦颜色")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.setDrawPointSize}},[e._v("设置涂鸦笔触大小")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.loadARResource}},[e._v("数字人渲染")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.setMiniAppHeight}},[e._v("设置⼩程序容器的⾼度")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.isARCameraFront}},[e._v("检查是否是前置摄像头")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.switchToFrontCamera}},[e._v("切换至前置摄像头")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.checkGbaEntitlement}},[e._v("GBA接口")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.closeWindow}},[e._v("关闭当前小程序页面")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.windowToBack}},[e._v("当前小程序页面退至后台")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.windowToForeground}},[e._v("当前小程序页面恢复到前台")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.showAnimation}},[e._v("显示全屏动画（TBD-表情雨场景）")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getVersions}},[e._v("获取当前OS与新通话SDK的版本")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.callSubtitleFunc}},[e._v("字幕控制（显示字幕）")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.callSubtitleFuncAdd}},[e._v("字幕控制（添加字幕）")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.callSubtitleFuncHide}},[e._v("字幕控制（隐藏字幕）")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.getControlBarStyle}},[e._v("获取悬浮控制胶囊按钮信息")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.setControlBarStyle}},[e._v("设置悬浮控制胶囊按钮")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.callVoiceHelperFuncSave}},[e._v("语音助手控制(保存文本)")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.callVoiceHelperFuncClose}},[e._v("语音助手控制(关闭语音助手时)")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.openRemoteApp}},[e._v("开启通话远端小程序")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small"},on:{click:e.closeRemoteApp}},[e._v("关闭通话远端小程")])],1),e._v(" "),n("div",{staticClass:"plugin"},[n("label",{staticClass:"label"},[e._v("通知回调插件")]),e._v(" "),n("el-button",{staticClass:"btn-2",attrs:{size:"small",disabled:""},on:{click:e.registerCallback}},[e._v("已自动注册所有全局通知回调插件\n    ")])],1),e._v(" "),n("el-button",{staticClass:"btn-3",attrs:{size:"small"},on:{click:e.readMsgList}},[e._v(e._s(e.clickResult?"关闭消息列表":"查看消息列表")+"\n  ")]),e._v(" "),e.clickResult?n("div",{staticClass:"mask"},[n("ul",{staticClass:"firstUl"},e._l(e.msgList,function(t,s){return n("li",{key:s},[n("p",[e._v(e._s(t.timeString+" "+t.name+(t.isSend?" send ":" receive ")+t.message))])])}),0)]):e._e()],1)},staticRenderFns:[]};var F=n("l2X3")(z,j,!1,function(e){n("jaWD")},"data-v-107687a2",null).exports,U=n("VDyS");s.default.use(U.a);var N=new U.a({routes:[{path:"/",redirect:"/sprite-check"},{path:"/sprite-check",name:"SpriteCheck",component:function(){return n.e(3).then(n.bind(null,"FXP8"))},meta:{title:"精灵状态检查"}},{path:"/sprite-claim",name:"SpriteClaim",component:function(){return Promise.all([n.e(0),n.e(4)]).then(n.bind(null,"b4kT"))},meta:{title:"认领精灵"}},{path:"/sprite-selection",name:"SpriteSelection",component:function(){return Promise.all([n.e(0),n.e(2)]).then(n.bind(null,"zax/"))},meta:{title:"选择精灵"}},{path:"/sprite-interaction",name:"SpriteInteraction",component:function(){return Promise.all([n.e(0),n.e(1)]).then(n.bind(null,"XGaV"))},meta:{title:"精灵互动"}}]});N.beforeEach(function(e,t,n){e.meta.title&&(document.title=e.meta.title),n()});var O=N,V=n("WAbp"),B=n.n(V);n("dHYs");s.default.use(B.a),s.default.config.productionTip=!1,new s.default({el:"#app",router:O,store:k.default,components:{App:F},template:"<App/>"})},dHYs:function(e,t){},jaWD:function(e,t){}},["NHnr"]);
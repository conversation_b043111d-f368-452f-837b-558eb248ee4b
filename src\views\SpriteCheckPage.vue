<template>
  <div class="sprite-container">
    <div class="check-content">
      <div class="loading-animation">
        <div class="loading-spinner"></div>
        <h2 class="loading-title">正在检查精灵状态...</h2>
        <p class="loading-subtitle">请稍候</p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import dataStorage from '@/utils/dataStorage'

export default {
  name: 'SpriteCheckPage',
  computed: {
    ...mapState('sprite', ['user'])
  },
  async mounted() {
    await this.checkSpriteStatus()
  },
  methods: {
    ...mapActions('sprite', ['initializeSpriteData']),

    /**
     * 检查精灵状态并导航到相应页面
     */
    async checkSpriteStatus() {
      try {
        // 初始化数据存储
        const plugins = await this.getPlugins()
        dataStorage.init(plugins)
        
        // 初始化精灵数据
        await this.initializeSpriteData()
        
        // 检查用户是否已认领精灵
        if (this.user.hasSprite && this.user.currentSpriteId) {
          // 已认领，直接进入互动页面
          console.log('用户已认领精灵，跳转到互动页面')
          this.$router.replace('/sprite-interaction')
        } else {
          // 未认领，跳转到认领页面
          console.log('用户未认领精灵，跳转到认领页面')
          this.$router.replace('/sprite-claim')
        }
        
      } catch (error) {
        console.error('检查精灵状态失败:', error)
        this.$message.error('初始化失败，请重试')
        
        // 出错时默认跳转到认领页面
        setTimeout(() => {
          this.$router.replace('/sprite-claim')
        }, 2000)
      }
    },

    /**
     * 获取插件对象
     * 这里需要从全局获取或通过其他方式注入
     */
    async getPlugins() {
      // 从全局获取plugins对象
      if (window.plugins) {
        return window.plugins
      }
      
      // 或者从现有的App.vue中获取
      if (this.$root.$children && this.$root.$children[0]) {
        const app = this.$root.$children[0]
        if (app.plugins) {
          return app.plugins
        }
      }
      
      // 动态导入plugins
      try {
        const pluginsModule = await import('@/js/jssdk.min-20231113.js')
        return pluginsModule.default
      } catch (error) {
        console.error('无法获取plugins对象:', error)
        throw new Error('插件初始化失败')
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '../styles/sprite.less';
@import '../styles/animations.less';

.sprite-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  
  .check-content {
    text-align: center;
    
    .loading-animation {
      .loading-title {
        font-size: 20px;
        color: #333;
        margin: 24px 0 8px;
        font-weight: normal;
      }
      
      .loading-subtitle {
        font-size: 14px;
        color: #666;
        margin: 0;
      }
    }
  }
}
</style>

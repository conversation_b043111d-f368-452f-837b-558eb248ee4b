/**
 * 错误处理和日志系统
 */

class ErrorHandler {
  constructor() {
    this.errors = []
    this.maxErrors = 100
    this.isInitialized = false
  }

  /**
   * 初始化错误处理
   */
  init() {
    if (this.isInitialized) return
    
    // 全局错误捕获
    window.addEventListener('error', (event) => {
      this.handleError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        timestamp: Date.now()
      })
    })
    
    // Promise 错误捕获
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: 'promise',
        message: (event.reason && event.reason.message) || 'Unhandled Promise Rejection',
        reason: event.reason,
        timestamp: Date.now()
      })
    })
    
    // Vue 错误捕获（如果可用）
    if (window.Vue) {
      window.Vue.config.errorHandler = (err, vm, info) => {
        this.handleError({
          type: 'vue',
          message: err.message,
          error: err,
          component: (vm && vm.$options && vm.$options.name) || 'Unknown',
          info: info,
          timestamp: Date.now()
        })
      }
    }
    
    this.isInitialized = true
    console.log('错误处理系统已初始化')
  }

  /**
   * 处理错误
   */
  handleError(errorInfo) {
    // 添加到错误列表
    this.errors.push(errorInfo)
    
    // 限制错误数量
    if (this.errors.length > this.maxErrors) {
      this.errors.shift()
    }
    
    // 控制台输出
    console.error('捕获到错误:', errorInfo)
    
    // 根据错误类型进行特殊处理
    this.processError(errorInfo)
  }

  /**
   * 处理特定类型的错误
   */
  processError(errorInfo) {
    switch (errorInfo.type) {
      case 'sprite':
        this.handleSpriteError(errorInfo)
        break
      case 'network':
        this.handleNetworkError(errorInfo)
        break
      case 'storage':
        this.handleStorageError(errorInfo)
        break
      case 'animation':
        this.handleAnimationError(errorInfo)
        break
      default:
        this.handleGenericError(errorInfo)
    }
  }

  /**
   * 处理精灵相关错误
   */
  handleSpriteError(errorInfo) {
    console.warn('精灵系统错误:', errorInfo.message)
    
    // 尝试恢复精灵状态
    if (errorInfo.recoverable) {
      this.attemptSpriteRecovery()
    }
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(errorInfo) {
    console.warn('网络错误:', errorInfo.message)
    
    // 可以在这里添加重试逻辑
    if (errorInfo.retryable) {
      this.scheduleRetry(errorInfo)
    }
  }

  /**
   * 处理存储错误
   */
  handleStorageError(errorInfo) {
    console.warn('存储错误:', errorInfo.message)
    
    // 尝试使用备用存储方案
    this.tryFallbackStorage()
  }

  /**
   * 处理动画错误
   */
  handleAnimationError(errorInfo) {
    console.warn('动画错误:', errorInfo.message)
    
    // 禁用有问题的动画
    this.disableProblematicAnimations()
  }

  /**
   * 处理通用错误
   */
  handleGenericError(errorInfo) {
    console.error('通用错误:', errorInfo.message)
  }

  /**
   * 尝试精灵状态恢复
   */
  async attemptSpriteRecovery() {
    try {
      console.log('尝试恢复精灵状态...')
      
      // 这里可以添加具体的恢复逻辑
      // 例如重新加载精灵数据、重置状态等
      
      const store = await import('@/store')
      await store.default.dispatch('sprite/loadSpriteData')
      
      console.log('精灵状态恢复成功')
    } catch (error) {
      console.error('精灵状态恢复失败:', error)
    }
  }

  /**
   * 安排重试
   */
  scheduleRetry(errorInfo) {
    const retryDelay = Math.min(1000 * Math.pow(2, errorInfo.retryCount || 0), 30000)
    
    setTimeout(() => {
      console.log('重试操作:', errorInfo.operation)
      // 这里可以添加具体的重试逻辑
    }, retryDelay)
  }

  /**
   * 尝试备用存储
   */
  tryFallbackStorage() {
    console.log('尝试使用备用存储方案')
    // 可以尝试使用 localStorage 作为备用
  }

  /**
   * 禁用有问题的动画
   */
  disableProblematicAnimations() {
    console.log('禁用有问题的动画')
    // 可以设置一个标志来禁用动画
    if (typeof window !== 'undefined') {
      window.DISABLE_ANIMATIONS = true
    }
  }

  /**
   * 记录自定义错误
   */
  logError(type, message, details = {}) {
    this.handleError({
      type,
      message,
      details,
      timestamp: Date.now(),
      custom: true
    })
  }

  /**
   * 记录警告
   */
  logWarning(message, details = {}) {
    console.warn('警告:', message, details)
  }

  /**
   * 记录信息
   */
  logInfo(message, details = {}) {
    console.log('信息:', message, details)
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.slice(-10)
    }
    
    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
    })
    
    return stats
  }

  /**
   * 清除错误记录
   */
  clearErrors() {
    this.errors = []
    console.log('错误记录已清除')
  }

  /**
   * 导出错误日志
   */
  exportErrorLog() {
    const log = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errors: this.errors
    }
    
    return JSON.stringify(log, null, 2)
  }

  /**
   * 检查系统健康状态
   */
  checkSystemHealth() {
    const health = {
      status: 'healthy',
      issues: [],
      recommendations: []
    }
    
    // 检查错误频率
    const recentErrors = this.errors.filter(error => 
      Date.now() - error.timestamp < 60000 // 最近1分钟
    )
    
    if (recentErrors.length > 5) {
      health.status = 'warning'
      health.issues.push('错误频率过高')
      health.recommendations.push('检查系统稳定性')
    }
    
    // 检查内存使用
    if (performance.memory && performance.memory.usedJSHeapSize > 100 * 1024 * 1024) {
      health.status = 'warning'
      health.issues.push('内存使用过高')
      health.recommendations.push('清理内存或重启应用')
    }
    
    // 检查存储错误
    const storageErrors = this.errors.filter(error => error.type === 'storage')
    if (storageErrors.length > 0) {
      health.status = 'warning'
      health.issues.push('存储系统异常')
      health.recommendations.push('检查存储权限和空间')
    }
    
    return health
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler()

// 自动初始化
if (typeof window !== 'undefined') {
  errorHandler.init()
}

export default errorHandler

// 导出便捷函数
export const logError = (type, message, details) => errorHandler.logError(type, message, details)
export const logWarning = (message, details) => errorHandler.logWarning(message, details)
export const logInfo = (message, details) => errorHandler.logInfo(message, details)
export const getErrorStats = () => errorHandler.getErrorStats()
export const checkSystemHealth = () => errorHandler.checkSystemHealth()
